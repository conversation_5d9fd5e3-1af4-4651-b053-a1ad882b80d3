﻿#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/install_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <spdlog/spdlog.h>

namespace sco {

class InstallCommand : public BaseCommand {
public:
    InstallCommand() = default;
    
    int execute() override {
        try {
            if (apps_.empty()) {
                std::cerr << "No apps specified for installation.\n";
                std::cout << "Usage: sco install <app1> [app2] [app3] ...\n";
                std::cout << "       sco install --global <app1> [app2] ...\n";
                std::cout << "       sco install --skip-dependencies <app1> ...\n";
                return 1;
            }
            
            SPDLOG_INFO("Install command called with {} apps", apps_.size());
            
            // Prepare install options
            InstallManager::InstallOptions options;
            options.global = global_;
            options.skip_dependencies = skip_dependencies_;
            options.force_reinstall = force_;
            options.no_cache = no_cache_;
            options.architecture = architecture_;
            
            // Show what we're about to install
            show_install_summary();
            
            // Perform installation
            auto start_time = std::chrono::steady_clock::now();
            auto result = InstallManager::install_apps(apps_, options);
            auto end_time = std::chrono::steady_clock::now();
            
            // Show results
            show_install_results(result);
            
            return result.success ? 0 : 1;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Install command failed: {}", e.what());
            std::cerr << "Installation failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "install"; }
    std::string get_description() const override { return "Install apps"; }
    
    // Setters for command options
    void set_apps(const std::vector<std::string>& apps) { apps_ = apps; }
    void set_global(bool global) { global_ = global; }
    void set_skip_dependencies(bool skip) { skip_dependencies_ = skip; }
    void set_force(bool force) { force_ = force; }
    void set_no_cache(bool no_cache) { no_cache_ = no_cache; }
    void set_architecture(const std::string& arch) { architecture_ = arch; }
    
private:
    std::vector<std::string> apps_;
    bool global_ = false;
    bool skip_dependencies_ = false;
    bool force_ = false;
    bool no_cache_ = false;
    std::string architecture_ = "64bit";
    
    void show_install_summary() {
        std::cout << "Installing " << apps_.size() << " app(s):\n";
        
        for (const auto& app : apps_) {
            std::cout << "  - " << app << "\n";
        }
        
        if (global_) {
            std::cout << "Installation mode: Global\n";
        } else {
            std::cout << "Installation mode: User\n";
        }
        
        if (skip_dependencies_) {
            std::cout << "Dependencies: Skipped\n";
        } else {
            std::cout << "Dependencies: Will be resolved automatically\n";
        }
        
        if (force_) {
            std::cout << "Force reinstall: Yes\n";
        }
        
        if (no_cache_) {
            std::cout << "Cache: Disabled\n";
        }
        
        std::cout << "Architecture: " << architecture_ << "\n";
        std::cout << "\n";
    }
    
    void show_install_results(const InstallManager::InstallResult& result) {
        std::cout << "\n";
        std::cout << "Installation completed in " << result.total_duration.count() << "ms\n";
        std::cout << "\n";
        
        if (result.success) {
            output::success("Installation successful!");
        } else {
            std::cout << "✗ Installation failed!\n";
            if (!result.error_message.empty()) {
                std::cout << "Error: " << result.error_message << "\n";
            }
        }
        
        if (!result.installed_apps.empty()) {
            std::cout << "\nSuccessfully installed:\n";
            for (const auto& app : result.installed_apps) {
                output::success("  " + app);
            }
        }
        
        if (!result.failed_apps.empty()) {
            std::cout << "\nFailed to install:\n";
            for (const auto& app : result.failed_apps) {
                std::cout << "  ✗ " << app << "\n";
            }
        }
        
        // Show post-installation information
        if (result.success && !result.installed_apps.empty()) {
            show_post_install_info();
        }
    }
    
    void show_post_install_info() {
        std::cout << "\nPost-installation notes:\n";
        
        // Check if shims directory is in PATH
        if (!ShimManager::is_shims_in_path()) {
            std::cout << "⚠ Warning: Shims directory is not in your PATH.\n";
            std::cout << "  Run 'sco checkup' for more information on how to fix this.\n";
        }
        
        // Show usage examples
        std::cout << "\nUsage examples:\n";
        std::cout << "  sco list                    # List installed apps\n";
        std::cout << "  sco search <query>          # Search for apps\n";
        std::cout << "  sco update                  # Update all apps\n";
        std::cout << "  sco uninstall <app>         # Uninstall an app\n";
    }
    
public:
    // Static helper methods for other commands to use
    static bool is_app_installed(const std::string& app_name) {
        return DependencyResolver::is_app_installed(app_name);
    }
    
    static std::vector<std::string> get_installed_apps() {
        std::vector<std::string> installed_apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return installed_apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Check if there's a "current" directory or version directories
                    auto current_dir = entry.path() / "current";
                    if (std::filesystem::exists(current_dir)) {
                        installed_apps.push_back(app_name);
                    } else {
                        // Check for version directories
                        bool has_version = false;
                        for (const auto& version_entry : std::filesystem::directory_iterator(entry.path())) {
                            if (version_entry.is_directory()) {
                                has_version = true;
                                break;
                            }
                        }
                        if (has_version) {
                            installed_apps.push_back(app_name);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to get installed apps: {}", e.what());
        }
        
        return installed_apps;
    }
    
    static std::string get_app_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        // Fallback: look for version directories
        try {
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory() && entry.path().filename() != "current") {
                    return entry.path().filename().string();
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_DEBUG("Failed to determine version for {}: {}", app_name, e.what());
        }
        
        return "unknown";
    }
    
    static std::string get_app_bucket(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("bucket")) {
                    return json["bucket"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
    
    // Validate apps before installation
    static bool validate_apps(const std::vector<std::string>& app_names, 
                            std::vector<std::string>& missing_apps) {
        missing_apps.clear();
        
        for (const auto& app_name : app_names) {
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                missing_apps.push_back(app_name);
            }
        }
        
        return missing_apps.empty();
    }
    
    // Show dependency information
    static void show_dependency_info(const std::vector<std::string>& app_names) {
        auto resolve_result = DependencyResolver::resolve(app_names);
        
        if (!resolve_result.success) {
            std::cout << "Dependency resolution failed:\n";
            
            if (!resolve_result.circular_dependencies.empty()) {
                std::cout << "Circular dependencies:\n";
                for (const auto& cycle : resolve_result.circular_dependencies) {
                    std::cout << "  " << cycle << "\n";
                }
            }
            
            if (!resolve_result.missing_dependencies.empty()) {
                std::cout << "Missing dependencies:\n";
                for (const auto& missing : resolve_result.missing_dependencies) {
                    std::cout << "  " << missing << "\n";
                }
            }
            return;
        }
        
        std::cout << "Installation order:\n";
        for (size_t i = 0; i < resolve_result.install_order.size(); ++i) {
            const auto& app = resolve_result.install_order[i];
            std::cout << "  " << (i + 1) << ". " << app;
            
            if (is_app_installed(app)) {
                std::cout << " (already installed)";
            }
            
            std::cout << "\n";
        }
    }
};

} // namespace sco
