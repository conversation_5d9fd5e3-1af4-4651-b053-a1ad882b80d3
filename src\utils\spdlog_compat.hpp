#pragma once

// Compatibility layer for spdlog - redirects to our output system
#include "output.hpp"
#include <string>
#include <sstream>

// Simple format function for basic {} replacement
template<typename... Args>
std::string simple_format(const std::string& format, Args&&... args) {
    std::ostringstream oss;
    std::string result = format;
    
    // Simple implementation - just convert args to string and replace first {}
    auto replace_next = [&](auto&& arg) {
        size_t pos = result.find("{}");
        if (pos != std::string::npos) {
            std::ostringstream arg_oss;
            arg_oss << arg;
            result.replace(pos, 2, arg_oss.str());
        }
    };
    
    (replace_next(args), ...);
    return result;
}

// SPDLOG macros compatibility
#define SPDLOG_INFO(...) sco::output::info(simple_format(__VA_ARGS__))
#define SPDLOG_WARN(...) sco::output::warn(simple_format(__VA_ARGS__))
#define SPDLOG_ERROR(...) sco::output::error(simple_format(__VA_ARGS__))
#define SPDLOG_DEBUG(...) sco::output::debug(simple_format(__VA_ARGS__))

// spdlog namespace compatibility
namespace spdlog {
    template<typename... Args>
    void info(const std::string& format, Args&&... args) {
        sco::output::info(simple_format(format, std::forward<Args>(args)...));
    }
    
    template<typename... Args>
    void warn(const std::string& format, Args&&... args) {
        sco::output::warn(simple_format(format, std::forward<Args>(args)...));
    }
    
    template<typename... Args>
    void error(const std::string& format, Args&&... args) {
        sco::output::error(simple_format(format, std::forward<Args>(args)...));
    }
    
    template<typename... Args>
    void debug(const std::string& format, Args&&... args) {
        sco::output::debug(simple_format(format, std::forward<Args>(args)...));
    }
}
