#include "download_manager.hpp"
#include "../core/config.hpp"
#include <iostream>

namespace sco {

// Aria2Downloader implementation
Aria2Downloader::Aria2Config Aria2Downloader::load_config_from_instance() {
    auto& config = Config::instance();
    
    Aria2Config aria2_config;
    aria2_config.enabled = config.get_bool("aria2-enabled");
    aria2_config.retry_wait = config.get_int("aria2-retry-wait");
    aria2_config.split = config.get_int("aria2-split");
    aria2_config.max_connection_per_server = config.get_int("aria2-max-connection-per-server");
    aria2_config.min_split_size = config.get("aria2-min-split-size");
    aria2_config.additional_options = config.get("aria2-options");
    aria2_config.warning_enabled = config.get_bool("aria2-warning-enabled");
    
    return aria2_config;
}

// DownloadManager implementation
bool DownloadManager::should_use_aria2() {
    auto& config = Config::instance();
    
    // Check if aria2 is enabled in config
    if (!config.get_bool("aria2-enabled")) {
        return false;
    }
    
    // Check if aria2c is available
    if (!Aria2Downloader::is_aria2_available()) {
        show_aria2_warning_if_needed();
        return false;
    }
    
    return true;
}

void DownloadManager::show_aria2_warning_if_needed() {
    auto& config = Config::instance();
    
    // Only show warning if enabled in config
    if (!config.get_bool("aria2-warning-enabled")) {
        return;
    }
    
    static bool warning_shown = false;
    if (!warning_shown) {
        std::cout << "Warning: aria2 is enabled but not installed.\n";
        std::cout << "To install aria2, run: scoop install aria2\n";
        std::cout << "To disable this warning, run: scoop config aria2-warning-enabled false\n";
        std::cout << "Falling back to built-in downloader...\n\n";
        warning_shown = true;
    }
}

} // namespace sco
