#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <algorithm>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

struct SearchResult {
    std::string name;
    std::string version;
    std::string bucket;
    std::string description;
};

class SearchCommand : public BaseCommand {
public:
    SearchCommand() = default;
    
    int execute() override {
        try {
            if (query_.empty()) {
                std::cerr << "Search query is required.\n";
                std::cout << "Usage: sco search <query>\n";
                return 1;
            }

            output::debug("Searching for: {}", query_);

            std::vector<SearchResult> results = search_apps(query_);

            if (results.empty()) {
                std::cout << "No apps found matching '" << query_ << "'.\n";
                return 0;
            }

            print_search_results(results);
            return 0;

        } catch (const std::exception& e) {
            output::error("Search command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "search"; }
    std::string get_description() const override { return "Search available apps"; }
    
    void set_query(const std::string& query) { query_ = query; }

private:
    std::string query_;
    
    std::vector<SearchResult> search_apps(const std::string& query) {
        std::vector<SearchResult> results;

        auto& config = Config::instance();
        config.load();
        auto buckets_dir = config.get_buckets_dir();

        if (!std::filesystem::exists(buckets_dir)) {
            output::debug("Buckets directory does not exist: {}", buckets_dir.string());
            return results;
        }

        try {
            // Search through all buckets
            for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (bucket_entry.is_directory()) {
                    std::string bucket_name = bucket_entry.path().filename().string();
                    search_in_bucket(bucket_entry.path(), bucket_name, query, results);
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Filesystem error while searching: {}", e.what());
        }

        // Sort results by name
        std::sort(results.begin(), results.end(),
            [](const SearchResult& a, const SearchResult& b) {
                return a.name < b.name;
            });

        return results;
    }
    
    void search_in_bucket(const std::filesystem::path& bucket_path, 
                          const std::string& bucket_name,
                          const std::string& query,
                          std::vector<SearchResult>& results) {
        
        auto manifests_dir = bucket_path / "bucket";
        if (!std::filesystem::exists(manifests_dir)) {
            // Try alternative structure
            manifests_dir = bucket_path;
        }
        
        if (!std::filesystem::exists(manifests_dir)) {
            output::debug("No manifests directory found in bucket: {}", bucket_name);
            return;
        }
        
        try {
            for (const auto& manifest_entry : std::filesystem::directory_iterator(manifests_dir)) {
                if (manifest_entry.is_regular_file() && 
                    manifest_entry.path().extension() == ".json") {
                    
                    std::string app_name = manifest_entry.path().stem().string();
                    
                    // Check if app name matches query (case-insensitive)
                    if (matches_query(app_name, query)) {
                        SearchResult result = parse_manifest(manifest_entry.path(), bucket_name);
                        if (!result.name.empty()) {
                            results.push_back(result);
                        }
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::debug("Error searching bucket {}: {}", bucket_name, e.what());
        }
    }
    
    bool matches_query(const std::string& app_name, const std::string& query) {
        // Convert both to lowercase for case-insensitive comparison
        std::string lower_app = app_name;
        std::string lower_query = query;
        
        std::transform(lower_app.begin(), lower_app.end(), lower_app.begin(), ::tolower);
        std::transform(lower_query.begin(), lower_query.end(), lower_query.begin(), ::tolower);
        
        return lower_app.find(lower_query) != std::string::npos;
    }
    
    SearchResult parse_manifest(const std::filesystem::path& manifest_path, 
                               const std::string& bucket_name) {
        SearchResult result;
        result.bucket = bucket_name;
        result.name = manifest_path.stem().string();
        
        try {
            std::ifstream file(manifest_path);
            if (!file.is_open()) {
                output::debug("Could not open manifest: {}", manifest_path.string());
                return result;
            }
            
            nlohmann::json manifest;
            file >> manifest;
            
            // Get version
            if (manifest.contains("version")) {
                result.version = manifest["version"].get<std::string>();
            } else {
                result.version = "unknown";
            }
            
            // Get description
            if (manifest.contains("description")) {
                result.description = manifest["description"].get<std::string>();
            } else {
                result.description = "";
            }
            
            // Truncate long descriptions
            if (result.description.length() > 60) {
                result.description = result.description.substr(0, 57) + "...";
            }
            
        } catch (const std::exception& e) {
            SPDLOG_DEBUG("Error parsing manifest {}: {}", manifest_path.string(), e.what());
            // Return partial result with just name and bucket
            result.version = "unknown";
            result.description = "";
        }
        
        return result;
    }
    
    void print_search_results(const std::vector<SearchResult>& results) {
        std::cout << "Search results for '" << query_ << "':\n\n";
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Version", true);
        table.add_column("Source", true);
        table.add_column("Description", true);
        
        for (const auto& result : results) {
            table.add_row({
                result.name,
                result.version,
                result.bucket,
                result.description
            });
        }
        
        table.print();
        
        std::cout << "\nFound " << results.size() << " app(s) matching '" << query_ << "'.\n";
        
        if (!results.empty()) {
            std::cout << "\nTo install an app, run: sco install <app_name>\n";
        }
    }
};

} // namespace sco
