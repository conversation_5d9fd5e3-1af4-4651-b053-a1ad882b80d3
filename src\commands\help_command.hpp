#pragma once

#include "base_command.hpp"
#include "../utils/table_formatter.hpp"
#include <CLI/CLI.hpp>
#include <iostream>
#include <vector>
#include <spdlog/spdlog.h>

namespace sco {

class HelpCommand : public BaseCommand {
public:
    HelpCommand() = default;
    
    int execute() override {
        if (command_name_.empty()) {
            show_general_help();
            return 0;
        } else {
            return show_command_help();
        }
    }
    
    std::string get_name() const override { return "help"; }
    std::string get_description() const override { return "Show help for a command"; }
    
    void set_command_name(const std::string& name) { command_name_ = name; }
    void set_app(CLI::App* app) { app_ = app; }
    
private:
    std::string command_name_;
    CLI::App* app_ = nullptr;

    void show_general_help() {
        std::cout << "Usage: sco <command> [<args>]\n\n";
        std::cout << "Available commands are listed below.\n\n";
        std::cout << "Type 'sco help <command>' to get more help for a specific command.\n\n";

        // Create table for commands
        TableFormatter table;
        table.add_column("Command", true);
        table.add_column("Summary", true);

        // Add all commands with descriptions
        std::vector<std::pair<std::string, std::string>> commands = {
            {"alias", "Manage scoop aliases"},
            {"bucket", "Manage Scoop buckets"},
            {"cache", "Show or clear the download cache"},
            {"cat", "Show content of specified manifest"},
            {"checkup", "Check for potential problems"},
            {"cleanup", "Cleanup apps by removing old versions"},
            {"config", "Get or set configuration values"},
            {"create", "Create a custom app manifest"},
            {"depends", "List dependencies for an app, in the order they'll be installed"},
            {"download", "Download apps in the cache folder and verify hashes"},
            {"export", "Exports installed apps, buckets (and optionally configs) in JSON format"},
            {"help", "Show help for a command"},
            {"hold", "Hold an app to disable updates"},
            {"home", "Opens the app homepage"},
            {"import", "Imports apps, buckets and configs from a Scoopfile in JSON format"},
            {"info", "Display information about an app"},
            {"install", "Install apps"},
            {"list", "List installed apps"},
            {"prefix", "Returns the path to the specified app"},
            {"reset", "Reset an app to resolve conflicts"},
            {"search", "Search available apps"},
            {"shim", "Manipulate Scoop shims"},
            {"status", "Show status and check for new app versions"},
            {"unhold", "Unhold an app to enable updates"},
            {"uninstall", "Uninstall an app"},
            {"update", "Update apps, or Scoop itself"},
            {"virustotal", "Look for app's hash or url on virustotal.com"},
            {"which", "Locate a shim/executable (similar to 'which' on Linux)"}
        };

        for (const auto& [command, summary] : commands) {
            table.add_row({command, summary});
        }

        table.print();
        output::debug("Help command executed successfully");
    }

    int show_command_help() {
        if (app_) {
            try {
                auto* subcommand = app_->get_subcommand(command_name_);
                if (subcommand) {
                    std::cout << subcommand->help();
                    output::debug("Help for command '{}' displayed", command_name_);
                    return 0;
                }
            } catch (const std::exception& e) {
                output::debug("Exception getting subcommand help: {}", e.what());
            }
        }

        std::cerr << "Unknown command: " << command_name_ << "\n";
        std::cout << "Type 'sco help' to see available commands.\n";
        output::warn("Unknown command requested: {}", command_name_);
        return 1;
    }
};

} // namespace sco
