#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>
#include <map>
#include <vector>
#include <algorithm>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class AliasCommand : public BaseCommand {
public:
    AliasCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "list") {
                return list_aliases();
            } else if (action_ == "add") {
                return add_alias();
            } else if (action_ == "remove" || action_ == "rm") {
                return remove_alias();
            } else {
                std::cout << "Unknown alias action: " << action_ << "\n";
                show_usage();
                return 1;
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Alias command failed: {}", e.what());
            std::cerr << "Alias command failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "alias"; }
    std::string get_description() const override { return "Manage scoop aliases"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_alias_name(const std::string& name) { alias_name_ = name; }
    void set_command(const std::string& command) { command_ = command; }
    
private:
    std::string action_;
    std::string alias_name_;
    std::string command_;
    
    void show_usage() {
        std::cout << "Usage:\n";
        std::cout << "  sco alias [list]                    # List all aliases\n";
        std::cout << "  sco alias add <name> <command>      # Add an alias\n";
        std::cout << "  sco alias remove <name>             # Remove an alias\n";
    }
    
    int list_aliases() {
        auto aliases = load_aliases();
        
        if (aliases.empty()) {
            std::cout << "No aliases are currently defined.\n";
            std::cout << "Use 'sco alias add <name> <command>' to create an alias.\n";
            return 0;
        }
        
        std::cout << "Defined aliases:\n\n";
        
        TableFormatter table;
        table.add_column("Alias", true);
        table.add_column("Command", true);
        
        for (const auto& alias : aliases) {
            table.add_row({alias.first, alias.second});
        }
        
        table.print();
        
        std::cout << "\nTotal: " << aliases.size() << " alias(es)\n";
        return 0;
    }
    
    int add_alias() {
        if (alias_name_.empty() || command_.empty()) {
            std::cout << "Both alias name and command are required.\n";
            std::cout << "Usage: sco alias add <name> <command>\n";
            return 1;
        }
        
        // Check for conflicts with existing commands
        if (is_builtin_command(alias_name_)) {
            std::cout << "Cannot create alias '" << alias_name_ << "': conflicts with built-in command.\n";
            return 1;
        }
        
        auto aliases = load_aliases();
        
        // Check if alias already exists
        if (aliases.find(alias_name_) != aliases.end()) {
            std::cout << "Alias '" << alias_name_ << "' already exists.\n";
            std::cout << "Current command: " << aliases[alias_name_] << "\n";
            std::cout << "Do you want to overwrite it? (y/N): ";
            
            std::string response;
            std::getline(std::cin, response);
            
            if (response != "y" && response != "Y") {
                std::cout << "Cancelled.\n";
                return 0;
            }
        }
        
        // Add the alias
        aliases[alias_name_] = command_;
        
        if (save_aliases(aliases)) {
            std::cout << "Alias '" << alias_name_ << "' created successfully.\n";
            std::cout << "Command: " << command_ << "\n";
            return 0;
        } else {
            std::cout << "Failed to save alias.\n";
            return 1;
        }
    }
    
    int remove_alias() {
        if (alias_name_.empty()) {
            std::cout << "Alias name is required.\n";
            std::cout << "Usage: sco alias remove <name>\n";
            return 1;
        }
        
        auto aliases = load_aliases();
        
        auto it = aliases.find(alias_name_);
        if (it == aliases.end()) {
            std::cout << "Alias '" << alias_name_ << "' does not exist.\n";
            return 1;
        }
        
        std::cout << "Removing alias '" << alias_name_ << "'...\n";
        std::cout << "Command: " << it->second << "\n";
        
        aliases.erase(it);
        
        if (save_aliases(aliases)) {
            std::cout << "Alias '" << alias_name_ << "' removed successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to save changes.\n";
            return 1;
        }
    }
    
    std::map<std::string, std::string> load_aliases() {
        std::map<std::string, std::string> aliases;
        
        auto& config = Config::instance();
        auto aliases_file = config.get_scoop_dir() / "aliases.json";
        
        if (!std::filesystem::exists(aliases_file)) {
            return aliases;
        }
        
        try {
            std::ifstream file(aliases_file);
            nlohmann::json json;
            file >> json;
            
            if (json.is_object()) {
                for (auto& [key, value] : json.items()) {
                    if (value.is_string()) {
                        aliases[key] = value.get<std::string>();
                    }
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to load aliases: {}", e.what());
        }
        
        return aliases;
    }
    
    bool save_aliases(const std::map<std::string, std::string>& aliases) {
        auto& config = Config::instance();
        auto aliases_file = config.get_scoop_dir() / "aliases.json";
        
        try {
            // Create directory if it doesn't exist
            std::filesystem::create_directories(aliases_file.parent_path());
            
            nlohmann::json json;
            for (const auto& alias : aliases) {
                json[alias.first] = alias.second;
            }
            
            std::ofstream file(aliases_file);
            if (!file.is_open()) {
                return false;
            }
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to save aliases: {}", e.what());
            return false;
        }
    }
    
    bool is_builtin_command(const std::string& name) {
        // List of built-in commands that cannot be aliased
        static const std::vector<std::string> builtin_commands = {
            "alias", "bucket", "cache", "cat", "checkup", "cleanup", "config",
            "create", "depends", "download", "export", "help", "hold", "home",
            "import", "info", "install", "list", "prefix", "reset", "search",
            "shim", "status", "unhold", "uninstall", "update", "virustotal", "which"
        };
        
        return std::find(builtin_commands.begin(), builtin_commands.end(), name) != builtin_commands.end();
    }
};

} // namespace sco
