#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace sco {

class ExportCommand : public BaseCommand {
public:
    ExportCommand() = default;
    
    int execute() override {
        try {
            SPDLOG_DEBUG("Export command called");
            
            auto export_data = create_export_data();
            
            if (output_file_.empty()) {
                // Output to stdout
                std::cout << export_data.dump(2) << std::endl;
            } else {
                // Output to file
                std::ofstream file(output_file_);
                if (!file.is_open()) {
                    std::cerr << "Failed to open output file: " << output_file_ << std::endl;
                    return 1;
                }
                
                file << export_data.dump(2) << std::endl;
                file.close();
                
                std::cout << "Exported to: " << output_file_ << std::endl;
            }
            
            return 0;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Export command failed: {}", e.what());
            std::cerr << "Export failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "export"; }
    std::string get_description() const override { return "Exports installed apps, buckets (and optionally configs) in JSON format"; }
    
    void set_output_file(const std::string& file) { output_file_ = file; }
    void set_include_config(bool include) { include_config_ = include; }
    
private:
    std::string output_file_;
    bool include_config_ = false;
    
    nlohmann::json create_export_data() {
        nlohmann::json export_data;
        
        // Add metadata
        export_data["version"] = "1.0";
        export_data["exported_at"] = get_current_timestamp();
        export_data["exported_by"] = "sco";
        
        // Export installed apps
        export_data["apps"] = export_installed_apps();
        
        // Export buckets
        export_data["buckets"] = export_buckets();
        
        // Export config if requested
        if (include_config_) {
            export_data["config"] = export_config();
        }
        
        return export_data;
    }
    
    nlohmann::json export_installed_apps() {
        nlohmann::json apps = nlohmann::json::array();
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Skip special directories
                    if (app_name == "scoop" || app_name == ".git") {
                        continue;
                    }
                    
                    auto app_info = get_app_info(app_name);
                    if (!app_info.is_null()) {
                        apps.push_back(app_info);
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Error reading apps directory: {}", e.what());
        }
        
        return apps;
    }
    
    nlohmann::json get_app_info(const std::string& app_name) {
        nlohmann::json app_info;
        
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        app_info["name"] = app_name;
        
        // Get installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json install_info;
                file >> install_info;
                
                if (install_info.contains("version")) {
                    app_info["version"] = install_info["version"];
                }
                if (install_info.contains("bucket")) {
                    app_info["bucket"] = install_info["bucket"];
                }
                if (install_info.contains("install_time")) {
                    app_info["installed_at"] = install_info["install_time"];
                }
                
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
                app_info["version"] = "unknown";
            }
        } else {
            app_info["version"] = "unknown";
        }
        
        // Check if globally installed
        auto global_apps_dir = config.get_global_apps_dir();
        auto global_app_dir = global_apps_dir / app_name;
        if (std::filesystem::exists(global_app_dir)) {
            app_info["global"] = true;
        } else {
            app_info["global"] = false;
        }
        
        return app_info;
    }
    
    nlohmann::json export_buckets() {
        nlohmann::json buckets = nlohmann::json::array();
        
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        
        if (!std::filesystem::exists(buckets_dir)) {
            return buckets;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (entry.is_directory()) {
                    std::string bucket_name = entry.path().filename().string();
                    
                    nlohmann::json bucket_info;
                    bucket_info["name"] = bucket_name;
                    
                    // Get source URL from git config
                    auto git_config = entry.path() / ".git" / "config";
                    if (std::filesystem::exists(git_config)) {
                        try {
                            std::ifstream file(git_config);
                            std::string line;
                            while (std::getline(file, line)) {
                                if (line.find("url = ") != std::string::npos) {
                                    bucket_info["source"] = line.substr(line.find("url = ") + 6);
                                    break;
                                }
                            }
                        } catch (const std::exception& e) {
                            SPDLOG_DEBUG("Failed to read git config for bucket {}: {}", bucket_name, e.what());
                            bucket_info["source"] = "unknown";
                        }
                    } else {
                        bucket_info["source"] = "local";
                    }
                    
                    buckets.push_back(bucket_info);
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Error reading buckets directory: {}", e.what());
        }
        
        return buckets;
    }
    
    nlohmann::json export_config() {
        nlohmann::json config_data;
        
        auto& config = Config::instance();
        
        // Export basic configuration
        config_data["scoop_dir"] = config.get_scoop_dir().string();
        config_data["apps_dir"] = config.get_apps_dir().string();
        config_data["cache_dir"] = config.get_cache_dir().string();
        config_data["buckets_dir"] = config.get_buckets_dir().string();
        config_data["shims_dir"] = config.get_shims_dir().string();
        
        // Export user configuration if it exists
        auto config_file = config.get_config_file();
        if (std::filesystem::exists(config_file)) {
            try {
                std::ifstream file(config_file);
                nlohmann::json user_config;
                file >> user_config;
                config_data["user_config"] = user_config;
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read user config: {}", e.what());
            }
        }
        
        return config_data;
    }
    
    std::string get_current_timestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::ostringstream oss;
        oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
        return oss.str();
    }
};

} // namespace sco
