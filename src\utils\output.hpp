#pragma once

#include <iostream>
#include <string>

#ifdef _WIN32
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

namespace sco {
namespace output {

// Global flags for controlling output
inline bool verbose = false;
inline bool quiet = false;

// Initialize output system with Unicode support
inline void init(bool verbose_mode = false, bool quiet_mode = false) {
    verbose = verbose_mode;
    quiet = quiet_mode;

#ifdef _WIN32
    // Enable UTF-8 output on Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // Enable ANSI escape sequences for better Unicode support
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != INVALID_HANDLE_VALUE) {
        DWORD dwMode = 0;
        if (GetConsoleMode(hOut, &dwMode)) {
            dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
            SetConsoleMode(hOut, dwMode);
        }
    }
#endif
}

// Output functions
inline void info(const std::string& message) {
    if (!quiet) {
        std::cout << message << std::endl;
    }
}

inline void warn(const std::string& message) {
    if (!quiet) {
        std::cout << "⚠ " << message << std::endl;
    }
}

inline void error(const std::string& message) {
    std::cerr << "✗ " << message << std::endl;
}

inline void debug(const std::string& message) {
    if (verbose && !quiet) {
        std::cout << "[DEBUG] " << message << std::endl;
    }
}

inline void success(const std::string& message) {
    if (!quiet) {
        std::cout << "✓ " << message << std::endl;
    }
}

} // namespace output
} // namespace sco
