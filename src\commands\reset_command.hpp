#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../utils/shim_manager.hpp"
#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include "../utils/output.hpp"

namespace sco {

class ResetCommand : public BaseCommand {
public:
    ResetCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                std::cerr << "App name(s) required.\n";
                std::cout << "Usage: sco reset <app1> [app2] ...\n";
                return 1;
            }

            SPDLOG_DEBUG("Reset command called for {} app(s)", app_names_.size());

            bool all_success = true;
            std::vector<std::string> reset_apps;
            std::vector<std::string> failed_apps;
            std::vector<std::string> not_installed;

            for (const auto& app_name : app_names_) {
                if (!is_app_installed(app_name)) {
                    not_installed.push_back(app_name);
                    continue;
                }

                if (reset_app(app_name)) {
                    reset_apps.push_back(app_name);
                } else {
                    failed_apps.push_back(app_name);
                    all_success = false;
                }
            }

            // Show results
            if (!not_installed.empty()) {
                std::cout << "The following apps are not installed:\n";
                for (const auto& app : not_installed) {
                    std::cout << "  - " << app << "\n";
                }
                std::cout << "\n";
            }

            if (!reset_apps.empty()) {
                std::cout << "Successfully reset:\n";
                for (const auto& app : reset_apps) {
                    std::cout << "  + " << app << "\n";
                }
            }

            if (!failed_apps.empty()) {
                std::cout << "Failed to reset:\n";
                for (const auto& app : failed_apps) {
                    std::cout << "  - " << app << "\n";
                }
            }

            return all_success ? 0 : 1;

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Reset command failed: {}", e.what());
            std::cerr << "Reset failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "reset"; }
    std::string get_description() const override { return "Reset an app to resolve conflicts"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    
private:
    std::vector<std::string> app_names_;
    
    bool is_app_installed(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        return std::filesystem::exists(app_dir);
    }
    
    bool reset_app(const std::string& app_name) {
        std::cout << "Resetting '" << app_name << "'...\n";
        
        try {
            // Step 1: Remove existing shims
            if (!remove_app_shims(app_name)) {
                SPDLOG_WARN("Failed to remove some shims for: {}", app_name);
                // Continue anyway
            }
            
            // Step 2: Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                std::cout << "  Warning: Could not find manifest for '" << app_name << "'\n";
                std::cout << "  Shims removed, but cannot recreate them.\n";
                return true; // Partial success
            }
            
            // Step 3: Get app installation directory
            auto& config = Config::instance();
            auto app_dir = config.get_apps_dir() / app_name;
            auto current_dir = app_dir / "current";
            
            if (!std::filesystem::exists(current_dir)) {
                std::cout << "  Warning: Current installation directory not found.\n";
                return false;
            }
            
            // Step 4: Recreate shims
            auto bin_entries = manifest.get_bin("64bit"); // Default to 64bit
            if (!bin_entries.empty()) {
                if (!ShimManager::create_shims_for_app(app_name, current_dir, bin_entries)) {
                    std::cout << "  Warning: Failed to recreate some shims.\n";
                    return false;
                }
            }
            
            // Step 5: Fix current directory symlink/junction if needed
            if (!fix_current_directory(app_name)) {
                SPDLOG_WARN("Failed to fix current directory for: {}", app_name);
                // Continue anyway
            }
            
            std::cout << "  + Reset completed successfully\n";
            return true;

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Exception during reset of {}: {}", app_name, e.what());
            std::cout << "  - Reset failed: " << e.what() << "\n";
            return false;
        }
    }
    
    bool remove_app_shims(const std::string& app_name) {
        try {
            // Get list of all shims and filter for this app
            auto all_shims = ShimManager::list_shims();

            bool all_success = true;
            for (const auto& shim : all_shims) {
                // Check if this shim belongs to the app
                if (shim.target_path.string().find(app_name) != std::string::npos) {
                    if (!ShimManager::remove_shim(shim.name)) {
                        SPDLOG_WARN("Failed to remove shim: {}", shim.name);
                        all_success = false;
                    }
                }
            }

            return all_success;

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to remove shims for {}: {}", app_name, e.what());
            return false;
        }
    }
    
    bool fix_current_directory(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        auto current_dir = app_dir / "current";
        
        if (!std::filesystem::exists(app_dir)) {
            return false;
        }
        
        try {
            // Find the latest version directory
            std::vector<std::string> versions;
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();
                    if (dir_name != "current" && dir_name != "backup") {
                        versions.push_back(dir_name);
                    }
                }
            }
            
            if (versions.empty()) {
                SPDLOG_ERROR("No version directories found for: {}", app_name);
                return false;
            }
            
            // Sort and get the latest version (simple string sort)
            std::sort(versions.begin(), versions.end());
            std::string latest_version = versions.back();
            auto latest_version_dir = app_dir / latest_version;
            
            // Remove existing current directory
            if (std::filesystem::exists(current_dir)) {
                std::filesystem::remove_all(current_dir);
            }
            
            // Create new current symlink/junction
#ifdef _WIN32
            // On Windows, create a junction point
            std::string command = "mklink /J \"" + current_dir.string() + "\" \"" + latest_version_dir.string() + "\"";
            int result = system(command.c_str());
            if (result != 0) {
                SPDLOG_WARN("Failed to create junction, using directory copy instead");
                std::filesystem::create_directories(current_dir);
                std::filesystem::copy(latest_version_dir, current_dir, 
                                    std::filesystem::copy_options::recursive);
            }
#else
            // On Unix-like systems, create a symbolic link
            std::filesystem::create_symlink(latest_version_dir, current_dir);
#endif
            
            SPDLOG_DEBUG("Fixed current directory for {} -> {}", app_name, latest_version);
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to fix current directory for {}: {}", app_name, e.what());
            return false;
        }
    }
};

} // namespace sco
