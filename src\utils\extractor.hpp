#pragma once

#include <string>
#include <filesystem>
#include <vector>
#include "output.hpp"
#include "../core/config.hpp"

#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#endif

namespace sco {

class Extractor {
public:
    struct ExtractResult {
        bool success = false;
        std::string error_message;
        std::vector<std::filesystem::path> extracted_files;
        std::filesystem::path extract_path;
    };
    
    static ExtractResult extract_archive(const std::filesystem::path& archive_path,
                                       const std::filesystem::path& extract_to,
                                       const std::string& extract_dir = "",
                                       const std::string& extract_to_subdir = "") {
        ExtractResult result;
        result.extract_path = extract_to;
        
        output::info("Extracting: {} -> {}", archive_path.string(), extract_to.string());
        
        try {
            // Ensure extract directory exists
            std::filesystem::create_directories(extract_to);
            
            // Determine archive type from extension
            std::string extension = archive_path.extension().string();
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
            
            // Try 7zip first for all formats (it handles most archive types)
            if (is_7zip_available()) {
                result = extract_with_7zip(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else if (extension == ".zip") {
                result = extract_zip(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else if (extension == ".7z") {
                result = extract_7z(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else if (extension == ".gz" || extension == ".tar" || extension == ".xz") {
                result = extract_tar(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else if (extension == ".msi") {
                result = extract_msi(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else if (extension == ".exe") {
                result = extract_exe(archive_path, extract_to, extract_dir, extract_to_subdir);
            } else {
                result.error_message = "Unsupported archive format: " + extension;
                SPDLOG_ERROR("Unsupported archive format: {}", extension);
                return result;
            }
            
            if (result.success) {
                SPDLOG_INFO("Extraction completed successfully");
            }
            
            return result;
            
        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
            SPDLOG_ERROR("Extraction failed: {}", e.what());
            return result;
        }
    }
    
private:
    static std::string find_7zip_executable() {
        std::vector<std::string> possible_7z_paths = {
            "7z.exe",
            "C:\\Program Files\\7-Zip\\7z.exe",
            "C:\\Program Files (x86)\\7-Zip\\7z.exe"
        };

        for (const auto& path : possible_7z_paths) {
            if (std::filesystem::exists(path)) {
                return path;
            }
        }

        // Check Scoop installation
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        auto seven_zip_path = apps_dir / "7zip" / "current" / "7z.exe";
        if (std::filesystem::exists(seven_zip_path)) {
            return seven_zip_path.string();
        }

        return "7z.exe"; // Fallback to PATH
    }

    static ExtractResult extract_with_7zip(const std::filesystem::path& archive_path,
                                          const std::filesystem::path& extract_to,
                                          const std::string& extract_dir,
                                          const std::string& extract_to_subdir) {
        ExtractResult result;

        std::string seven_z_path = find_7zip_executable();

        // Create a temporary batch file to avoid escaping issues
        auto temp_batch = extract_to / "temp_extract.bat";
        std::ofstream batch_file(temp_batch);
        if (!batch_file.is_open()) {
            result.error_message = "Failed to create temporary batch file";
            return result;
        }

        batch_file << "@echo off\n";
        batch_file << "\"" << seven_z_path << "\" x \"" << archive_path.string() << "\" -o\"" << extract_to.string() << "\" -y\n";
        batch_file.close();

        std::string command = temp_batch.string();

        SPDLOG_INFO("Executing 7zip via batch file: {}", command);
        std::cout << "Executing: " << command << std::endl;

        int exit_code = system(command.c_str());
        SPDLOG_INFO("7zip exit code: {}", exit_code);

        // Clean up temporary batch file
        try {
            std::filesystem::remove(temp_batch);
        } catch (const std::exception& e) {
            SPDLOG_WARN("Failed to remove temporary batch file: {}", e.what());
        }

        if (exit_code == 0) {
            result.success = true;
            result.extract_path = extract_to;

            // Check if we extracted a .tar file that needs further extraction
            std::string filename = archive_path.filename().string();
            std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

            if (filename.find(".tar.") != std::string::npos) {
                // This was a .tar.gz, .tar.xz, etc. - need to extract the .tar file too
                std::string tar_filename = archive_path.stem().string(); // Remove .xz/.gz
                auto tar_path = extract_to / tar_filename;

                if (std::filesystem::exists(tar_path)) {
                    SPDLOG_DEBUG("Extracting nested tar file: {}", tar_path.string());

                    // Create another temporary batch file for tar extraction
                    auto temp_tar_batch = extract_to / "temp_extract_tar.bat";
                    std::ofstream tar_batch_file(temp_tar_batch);
                    if (tar_batch_file.is_open()) {
                        tar_batch_file << "@echo off\n";
                        tar_batch_file << "\"" << seven_z_path << "\" x \"" << tar_path.string() << "\" -o\"" << extract_to.string() << "\" -y\n";
                        tar_batch_file.close();

                        int tar_exit_code = system(temp_tar_batch.string().c_str());
                        if (tar_exit_code == 0) {
                            // Remove the intermediate .tar file
                            std::filesystem::remove(tar_path);
                        } else {
                            SPDLOG_WARN("Failed to extract nested tar file, exit code: {}", tar_exit_code);
                        }

                        // Clean up tar batch file
                        try {
                            std::filesystem::remove(temp_tar_batch);
                        } catch (const std::exception& e) {
                            SPDLOG_WARN("Failed to remove temporary tar batch file: {}", e.what());
                        }
                    }
                }
            }

            if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
            }
        } else {
            result.error_message = "7zip extraction failed with exit code: " + std::to_string(exit_code);
        }

        return result;
    }

    static ExtractResult extract_zip(const std::filesystem::path& archive_path,
                                   const std::filesystem::path& extract_to,
                                   const std::string& extract_dir,
                                   const std::string& extract_to_subdir) {
        ExtractResult result;
        
#ifdef _WIN32
        // Use PowerShell to extract ZIP files
        std::string ps_command = "Expand-Archive -Path '" + archive_path.string() + 
                                "' -DestinationPath '" + extract_to.string() + "' -Force";
        
        std::string full_command = "powershell.exe -Command \"" + ps_command + "\"";
        
        SPDLOG_DEBUG("Executing: {}", full_command);
        
        int exit_code = system(full_command.c_str());
        if (exit_code == 0) {
            result.success = true;
            result.extract_path = extract_to;
            
            // Handle extract_dir and extract_to_subdir if specified
            if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
            }
        } else {
            result.error_message = "PowerShell extraction failed with exit code: " + std::to_string(exit_code);
        }
#else
        result.error_message = "ZIP extraction not implemented for non-Windows platforms";
#endif
        
        return result;
    }
    
    static ExtractResult extract_7z(const std::filesystem::path& archive_path,
                                  const std::filesystem::path& extract_to,
                                  const std::string& extract_dir,
                                  const std::string& extract_to_subdir) {
        ExtractResult result;
        
        // Try to find 7z executable
        std::vector<std::string> possible_7z_paths = {
            "7z.exe",
            "C:\\Program Files\\7-Zip\\7z.exe",
            "C:\\Program Files (x86)\\7-Zip\\7z.exe"
        };
        
        std::string seven_z_path;
        for (const auto& path : possible_7z_paths) {
            if (std::filesystem::exists(path)) {
                seven_z_path = path;
                break;
            }
        }
        
        if (seven_z_path.empty()) {
            result.error_message = "7z.exe not found. Please install 7-Zip.";
            return result;
        }
        
        std::string command = "\"" + seven_z_path + "\" x \"" + archive_path.string() + 
                             "\" -o\"" + extract_to.string() + "\" -y";
        
        SPDLOG_DEBUG("Executing: {}", command);
        
        int exit_code = system(command.c_str());
        if (exit_code == 0) {
            result.success = true;
            result.extract_path = extract_to;
            
            if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
            }
        } else {
            result.error_message = "7z extraction failed with exit code: " + std::to_string(exit_code);
        }
        
        return result;
    }
    
    static ExtractResult extract_tar(const std::filesystem::path& archive_path,
                                   const std::filesystem::path& extract_to,
                                   const std::string& extract_dir,
                                   const std::string& extract_to_subdir) {
        ExtractResult result;
        
        // Use tar command if available (Windows 10 has built-in tar)
        std::string command = "tar -xf \"" + archive_path.string() + "\" -C \"" + extract_to.string() + "\"";
        
        SPDLOG_DEBUG("Executing: {}", command);
        
        int exit_code = system(command.c_str());
        if (exit_code == 0) {
            result.success = true;
            result.extract_path = extract_to;
            
            if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
            }
        } else {
            result.error_message = "tar extraction failed with exit code: " + std::to_string(exit_code);
        }
        
        return result;
    }
    
    static ExtractResult extract_msi(const std::filesystem::path& archive_path,
                                   const std::filesystem::path& extract_to,
                                   const std::string& extract_dir,
                                   const std::string& extract_to_subdir) {
        ExtractResult result;
        
#ifdef _WIN32
        // Use msiexec to extract MSI files
        std::string command = "msiexec /a \"" + archive_path.string() + 
                             "\" /qn TARGETDIR=\"" + extract_to.string() + "\"";
        
        SPDLOG_DEBUG("Executing: {}", command);
        
        int exit_code = system(command.c_str());
        if (exit_code == 0) {
            result.success = true;
            result.extract_path = extract_to;
            
            if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
            }
        } else {
            result.error_message = "MSI extraction failed with exit code: " + std::to_string(exit_code);
        }
#else
        result.error_message = "MSI extraction not supported on non-Windows platforms";
#endif
        
        return result;
    }
    
    static ExtractResult extract_exe(const std::filesystem::path& archive_path,
                                   const std::filesystem::path& extract_to,
                                   const std::string& extract_dir,
                                   const std::string& extract_to_subdir) {
        ExtractResult result;
        
        // Try common self-extracting archive options
        std::vector<std::string> extract_options = {
            "/S /D=\"" + extract_to.string() + "\"",  // NSIS
            "/SILENT /DIR=\"" + extract_to.string() + "\"",  // InnoSetup
            "/extract:\"" + extract_to.string() + "\"",  // WinRAR SFX
            "/x \"" + extract_to.string() + "\""  // 7z SFX
        };
        
        for (const auto& option : extract_options) {
            std::string command = "\"" + archive_path.string() + "\" " + option;
            
            SPDLOG_DEBUG("Trying: {}", command);
            
            int exit_code = system(command.c_str());
            if (exit_code == 0) {
                result.success = true;
                result.extract_path = extract_to;
                
                if (!extract_dir.empty() || !extract_to_subdir.empty()) {
                    result = handle_extract_dir_options(result, extract_dir, extract_to_subdir);
                }
                return result;
            }
        }
        
        result.error_message = "Failed to extract EXE file with any known method";
        return result;
    }
    
    static ExtractResult handle_extract_dir_options(ExtractResult result,
                                                   const std::string& extract_dir,
                                                   const std::string& extract_to_subdir) {
        if (!result.success) {
            return result;
        }
        
        try {
            std::filesystem::path current_path = result.extract_path;
            
            // Handle extract_dir (move from subdirectory to parent)
            if (!extract_dir.empty()) {
                std::filesystem::path source_dir = current_path / extract_dir;
                if (std::filesystem::exists(source_dir)) {
                    // Move contents from extract_dir to parent
                    for (const auto& entry : std::filesystem::directory_iterator(source_dir)) {
                        std::filesystem::path dest = current_path / entry.path().filename();
                        std::filesystem::rename(entry.path(), dest);
                    }
                    // Remove empty extract_dir
                    std::filesystem::remove(source_dir);
                }
            }
            
            // Handle extract_to (move to subdirectory)
            if (!extract_to_subdir.empty()) {
                std::filesystem::path target_dir = current_path / extract_to_subdir;
                std::filesystem::create_directories(target_dir);
                
                // Move all files to subdirectory
                for (const auto& entry : std::filesystem::directory_iterator(current_path)) {
                    if (entry.path().filename() != extract_to_subdir) {
                        std::filesystem::path dest = target_dir / entry.path().filename();
                        std::filesystem::rename(entry.path(), dest);
                    }
                }
                
                result.extract_path = target_dir;
            }
            
        } catch (const std::exception& e) {
            SPDLOG_WARN("Failed to handle extract directory options: {}", e.what());
            // Don't fail the entire extraction for this
        }
        
        return result;
    }
    
public:
    // Check if 7zip is available
    static bool is_7zip_available() {
        // Check if 7z is available in PATH or common locations
        std::vector<std::string> possible_7z_paths = {
            "7z",
            "7z.exe",
            "C:\\Program Files\\7-Zip\\7z.exe",
            "C:\\Program Files (x86)\\7-Zip\\7z.exe"
        };

        for (const auto& path : possible_7z_paths) {
            std::string test_command = "\"" + path + "\" > nul 2>&1";
            if (system(test_command.c_str()) != 9009) { // 9009 = command not found
                return true;
            }
        }

        // Check if 7zip is installed via Scoop
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        auto seven_zip_path = apps_dir / "7zip" / "current" / "7z.exe";
        if (std::filesystem::exists(seven_zip_path)) {
            return true;
        }

        return false;
    }

    // Check if file is an archive that we can extract
    static bool is_extractable(const std::filesystem::path& file_path) {
        std::string extension = file_path.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

        // Also check for .tar.xz, .tar.gz etc.
        std::string filename = file_path.filename().string();
        std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

        return extension == ".zip" || extension == ".7z" || extension == ".gz" ||
               extension == ".tar" || extension == ".msi" || extension == ".exe" ||
               extension == ".xz" || filename.find(".tar.") != std::string::npos;
    }
    
    // Get list of files in archive (without extracting)
    static std::vector<std::string> list_archive_contents(const std::filesystem::path& archive_path) {
        std::vector<std::string> contents;
        
        // This would require implementing archive reading without extraction
        // For now, return empty list
        SPDLOG_WARN("Archive content listing not implemented yet");
        
        return contents;
    }
    
    // Estimate extraction size
    static size_t estimate_extraction_size(const std::filesystem::path& archive_path) {
        // For now, return file size * 3 as rough estimate
        try {
            return std::filesystem::file_size(archive_path) * 3;
        } catch (const std::exception&) {
            return 0;
        }
    }
};

} // namespace sco
