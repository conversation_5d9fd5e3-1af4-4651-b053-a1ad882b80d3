#pragma once

#include <string>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <functional>
#include <algorithm>
#include <vector>
#include <cstdlib>
#include "output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <wininet.h>
#include <wincrypt.h>
#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "advapi32.lib")
#endif

namespace sco {

// Forward declaration
class Config;

struct DownloadProgress {
    size_t total_bytes = 0;
    size_t downloaded_bytes = 0;
    double speed_bps = 0.0;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point last_update;
    
    double get_progress_percent() const {
        if (total_bytes == 0) return 0.0;
        return (static_cast<double>(downloaded_bytes) / total_bytes) * 100.0;
    }
    
    std::string get_speed_string() const {
        if (speed_bps < 1024) {
            return std::to_string(static_cast<int>(speed_bps)) + " B/s";
        } else if (speed_bps < 1024 * 1024) {
            return std::to_string(static_cast<int>(speed_bps / 1024)) + " KB/s";
        } else {
            return std::to_string(static_cast<int>(speed_bps / (1024 * 1024))) + " MB/s";
        }
    }
    
    std::string get_size_string(size_t bytes) const {
        if (bytes < 1024) {
            return std::to_string(bytes) + " B";
        } else if (bytes < 1024 * 1024) {
            return std::to_string(bytes / 1024) + " KB";
        } else {
            return std::to_string(bytes / (1024 * 1024)) + " MB";
        }
    }
};

// Aria2 downloader class for multi-connection downloads
class Aria2Downloader {
public:
    struct Aria2Config {
        bool enabled = true;
        int retry_wait = 2;
        int split = 5;
        int max_connection_per_server = 5;
        std::string min_split_size = "5M";
        std::string additional_options = "";
        bool warning_enabled = true;
    };

    struct DownloadResult {
        bool success = false;
        std::string error_message;
        std::filesystem::path downloaded_file;
        size_t total_bytes = 0;
        std::chrono::milliseconds duration{0};
    };

    // Check if aria2c is available in PATH
    static bool is_aria2_available() {
#ifdef _WIN32
        int result = std::system("aria2c --version >nul 2>&1");
        return result == 0;
#else
        int result = std::system("aria2c --version >/dev/null 2>&1");
        return result == 0;
#endif
    }

    // Download file using aria2c
    static DownloadResult download_file(const std::string& url,
                                      const std::filesystem::path& output_path,
                                      const Aria2Config& config = {}) {
        DownloadResult result;
        result.downloaded_file = output_path;

        if (!is_aria2_available()) {
            result.error_message = "aria2c is not available in PATH";
            return result;
        }

        auto start_time = std::chrono::steady_clock::now();

        // Ensure output directory exists
        std::filesystem::create_directories(output_path.parent_path());

        // Build aria2c command
        std::vector<std::string> args = {
            "aria2c",
            "--continue=true",
            "--max-tries=5",
            "--retry-wait=" + std::to_string(config.retry_wait),
            "--split=" + std::to_string(config.split),
            "--max-connection-per-server=" + std::to_string(config.max_connection_per_server),
            "--min-split-size=" + config.min_split_size,
            "--console-log-level=warn",
            "--summary-interval=0",
            "--download-result=hide",
            "--dir=" + output_path.parent_path().string(),
            "--out=" + output_path.filename().string(),
            url
        };

        // Add additional options if specified
        if (!config.additional_options.empty()) {
            // Split additional options by space (simple parsing)
            std::istringstream iss(config.additional_options);
            std::string option;
            while (iss >> option) {
                args.push_back(option);
            }
        }

        // Build command string
        std::string command;
        for (const auto& arg : args) {
            if (!command.empty()) command += " ";
            // Quote arguments that contain spaces
            if (arg.find(' ') != std::string::npos) {
                command += "\"" + arg + "\"";
            } else {
                command += arg;
            }
        }

        SPDLOG_DEBUG("Executing aria2c command: {}", command);

        // Execute aria2c
        int exit_code = std::system(command.c_str());

        auto end_time = std::chrono::steady_clock::now();
        result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        if (exit_code == 0 && std::filesystem::exists(output_path)) {
            result.success = true;
            result.total_bytes = std::filesystem::file_size(output_path);
            SPDLOG_INFO("aria2c download completed: {} bytes in {}ms",
                       result.total_bytes, result.duration.count());
        } else {
            result.success = false;
            result.error_message = "aria2c download failed with exit code: " + std::to_string(exit_code);
            SPDLOG_ERROR("aria2c download failed: {}", result.error_message);
        }

        return result;
    }

    // Load aria2 configuration from Config instance
    static Aria2Config load_config_from_instance() {
        auto& config = Config::instance();

        Aria2Config aria2_config;
        aria2_config.enabled = config.get_bool("aria2-enabled");
        aria2_config.retry_wait = config.get_int("aria2-retry-wait");
        aria2_config.split = config.get_int("aria2-split");
        aria2_config.max_connection_per_server = config.get_int("aria2-max-connection-per-server");
        aria2_config.min_split_size = config.get("aria2-min-split-size");
        aria2_config.additional_options = config.get("aria2-options");
        aria2_config.warning_enabled = config.get_bool("aria2-warning-enabled");

        return aria2_config;
    }
};

class DownloadManager {
public:
    using ProgressCallback = std::function<void(const DownloadProgress&)>;

    struct DownloadResult {
        bool success = false;
        std::string error_message;
        std::filesystem::path downloaded_file;
        size_t total_bytes = 0;
        std::chrono::milliseconds duration{0};
    };

    // Generate Scoop-compatible cache filename: app#version#url_hash.ext
    static std::string generate_cache_filename(const std::string& app_name,
                                             const std::string& version,
                                             const std::string& url,
                                             const std::string& = "") {
        // Extract file extension from URL
        std::filesystem::path url_path(url);
        std::string extension = url_path.extension().string();

        // Generate SHA256 hash from URL (like Scoop does)
        std::string url_hash = calculate_sha256_from_url(url);

        return app_name + "#" + version + "#" + url_hash + extension;
    }

private:
    // Calculate SHA256 hash from URL (first 7 characters, like Scoop)
    static std::string calculate_sha256_from_url(const std::string& url) {
#ifdef _WIN32
        // Use Windows CryptoAPI for SHA256
        HCRYPTPROV hProv = 0;
        HCRYPTHASH hHash = 0;
        BYTE hash[32]; // SHA256 produces 32 bytes
        DWORD hash_len = 32;

        if (CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
            if (CryptCreateHash(hProv, CALG_SHA_256, 0, 0, &hHash)) {
                if (CryptHashData(hHash, (BYTE*)url.c_str(), url.length(), 0)) {
                    if (CryptGetHashParam(hHash, HP_HASHVAL, hash, &hash_len, 0)) {
                        // Convert to lowercase hex string and take first 7 characters
                        std::ostringstream oss;
                        for (int i = 0; i < 4; ++i) { // 4 bytes = 8 hex chars, we need 7
                            oss << std::hex << std::setfill('0') << std::setw(2) << (unsigned int)hash[i];
                        }
                        std::string result = oss.str();
                        CryptDestroyHash(hHash);
                        CryptReleaseContext(hProv, 0);
                        return result.substr(0, 7); // Take first 7 characters
                    }
                }
                CryptDestroyHash(hHash);
            }
            CryptReleaseContext(hProv, 0);
        }
#endif

        // Fallback: use std::hash
        auto url_hash = std::hash<std::string>{}(url);
        std::ostringstream oss;
        oss << std::hex << url_hash;
        std::string hash_str = oss.str();

        // Take first 7 characters and convert to lowercase
        if (hash_str.length() > 7) {
            hash_str = hash_str.substr(0, 7);
        }

        // Ensure lowercase
        std::transform(hash_str.begin(), hash_str.end(), hash_str.begin(), ::tolower);

        return hash_str;
    }

public:
    
    static DownloadResult download_file(const std::string& url,
                                      const std::filesystem::path& output_path,
                                      ProgressCallback progress_callback = nullptr) {
        DownloadResult result;
        result.downloaded_file = output_path;

        SPDLOG_INFO("Downloading: {} -> {}", url, output_path.string());

        try {
            // Ensure output directory exists
            std::filesystem::create_directories(output_path.parent_path());

            // Try aria2 first if enabled and available
            if (should_use_aria2()) {
                auto aria2_config = Aria2Downloader::load_config_from_instance();
                auto aria2_result = Aria2Downloader::download_file(url, output_path, aria2_config);

                if (aria2_result.success) {
                    // Convert Aria2Downloader::DownloadResult to DownloadManager::DownloadResult
                    result.success = aria2_result.success;
                    result.downloaded_file = aria2_result.downloaded_file;
                    result.total_bytes = aria2_result.total_bytes;
                    result.duration = aria2_result.duration;
                    return result;
                } else {
                    output::warn("aria2 download failed, falling back to built-in downloader: {}",
                               aria2_result.error_message);
                }
            }

            // Fallback to built-in downloader
#ifdef _WIN32
            return download_with_wininet(url, output_path, progress_callback);
#else
            return download_with_curl(url, output_path, progress_callback);
#endif
        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
            output::error("Download failed: {}", e.what());
            return result;
        }
    }
    
    static bool verify_hash(const std::filesystem::path& file_path, const std::string& expected_hash) {
        if (expected_hash.empty()) {
            output::debug("No hash provided for verification");
            return true; // No hash to verify
        }

        // TODO: Implement proper SHA256 verification
        output::warn("Hash verification temporarily disabled - file: {}", file_path.string());
        output::debug("Expected hash: {}", expected_hash);
        return true; // Temporarily skip hash verification

        try {
            std::string actual_hash = calculate_sha256(file_path);
            bool matches = (actual_hash == expected_hash);

            if (matches) {
                output::debug("Hash verification successful for: {}", file_path.string());
            } else {
                output::error("Hash verification failed for: {}", file_path.string());
                output::error("Expected: {}", expected_hash);
                output::error("Actual:   {}", actual_hash);
            }

            return matches;
        } catch (const std::exception& e) {
            output::error("Hash calculation failed: {}", e.what());
            return false;
        }
    }
    
private:
#ifdef _WIN32
    static DownloadResult download_with_wininet(const std::string& url, 
                                               const std::filesystem::path& output_path,
                                               ProgressCallback progress_callback) {
        DownloadResult result;
        auto start_time = std::chrono::steady_clock::now();
        
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            result.error_message = "Failed to initialize WinINet";
            return result;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        if (!hUrl) {
            InternetCloseHandle(hInternet);
            result.error_message = "Failed to open URL";
            return result;
        }
        
        // Get content length
        DWORD content_length = 0;
        DWORD buffer_size = sizeof(content_length);
        DWORD index = 0;
        
        HttpQueryInfoA(hUrl, HTTP_QUERY_CONTENT_LENGTH | HTTP_QUERY_FLAG_NUMBER,
                      &content_length, &buffer_size, &index);
        
        std::ofstream output_file(output_path, std::ios::binary);
        if (!output_file.is_open()) {
            InternetCloseHandle(hUrl);
            InternetCloseHandle(hInternet);
            result.error_message = "Failed to create output file";
            return result;
        }
        
        DownloadProgress progress;
        progress.total_bytes = content_length;
        progress.start_time = start_time;
        progress.last_update = start_time;
        
        const DWORD buffer_size_read = 8192;
        char buffer[buffer_size_read];
        DWORD bytes_read;
        
        while (InternetReadFile(hUrl, buffer, buffer_size_read, &bytes_read) && bytes_read > 0) {
            output_file.write(buffer, bytes_read);
            progress.downloaded_bytes += bytes_read;
            
            // Update progress
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - progress.last_update);
            
            if (elapsed.count() >= 100 || progress.downloaded_bytes == progress.total_bytes) { // Update every 100ms
                auto total_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - progress.start_time);
                if (total_elapsed.count() > 0) {
                    progress.speed_bps = (static_cast<double>(progress.downloaded_bytes) * 1000.0) / total_elapsed.count();
                }
                progress.last_update = now;
                
                if (progress_callback) {
                    progress_callback(progress);
                }
            }
        }
        
        output_file.close();
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        auto end_time = std::chrono::steady_clock::now();
        result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        result.total_bytes = progress.downloaded_bytes;
        result.success = true;

        output::info("Download completed: {} bytes in {}ms",
                   result.total_bytes, result.duration.count());

        return result;
    }
#else
    static DownloadResult download_with_curl(const std::string& url, 
                                           const std::filesystem::path& output_path,
                                           ProgressCallback progress_callback) {
        DownloadResult result;
        result.error_message = "cURL download not implemented yet";
        return result;
    }
#endif
    
    static std::string calculate_sha256(const std::filesystem::path& file_path) {
        // For now, return empty string - proper SHA256 implementation would require
        // additional dependencies like OpenSSL or Windows CryptoAPI
        SPDLOG_WARN("SHA256 calculation not implemented yet");
        return "";
    }
    
public:
    // Utility function to show progress bar
    static void show_progress_bar(const DownloadProgress& progress) {
        const int bar_width = 50;
        double percent = progress.get_progress_percent();
        int filled = static_cast<int>(percent * bar_width / 100.0);
        
        std::cout << "\r[";
        for (int i = 0; i < bar_width; ++i) {
            if (i < filled) {
                std::cout << "=";
            } else if (i == filled) {
                std::cout << ">";
            } else {
                std::cout << " ";
            }
        }
        std::cout << "] " << std::fixed << std::setprecision(1) << percent << "% ";
        
        if (progress.total_bytes > 0) {
            std::cout << progress.get_size_string(progress.downloaded_bytes) 
                     << "/" << progress.get_size_string(progress.total_bytes) << " ";
        }
        
        std::cout << progress.get_speed_string();
        std::cout.flush();
        
        if (progress.downloaded_bytes == progress.total_bytes && progress.total_bytes > 0) {
            std::cout << std::endl;
        }
    }
    
    // Download with default progress display
    static DownloadResult download_with_progress(const std::string& url, 
                                                const std::filesystem::path& output_path) {
        return download_file(url, output_path, [](const DownloadProgress& progress) {
            show_progress_bar(progress);
        });
    }
    
    // Check if URL is accessible
    static bool check_url_accessible(const std::string& url) {
#ifdef _WIN32
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            return false;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        bool accessible = (hUrl != NULL);
        
        if (hUrl) InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        return accessible;
#else
        return false; // Not implemented for non-Windows
#endif
    }
    
    // Check if aria2 should be used for downloads
    static bool should_use_aria2() {
        auto& config = Config::instance();

        // Check if aria2 is enabled in config
        if (!config.get_bool("aria2-enabled")) {
            return false;
        }

        // Check if aria2c is available
        if (!Aria2Downloader::is_aria2_available()) {
            show_aria2_warning_if_needed();
            return false;
        }

        return true;
    }

    // Show aria2 warning if not installed but enabled
    static void show_aria2_warning_if_needed() {
        auto& config = Config::instance();

        // Only show warning if enabled in config
        if (!config.get_bool("aria2-warning-enabled")) {
            return;
        }

        static bool warning_shown = false;
        if (!warning_shown) {
            std::cout << "Warning: aria2 is enabled but not installed.\n";
            std::cout << "To install aria2, run: scoop install aria2\n";
            std::cout << "To disable this warning, run: scoop config aria2-warning-enabled false\n";
            std::cout << "Falling back to built-in downloader...\n\n";
            warning_shown = true;
        }
    }

    // Get file size from URL without downloading
    static size_t get_remote_file_size(const std::string& url) {
#ifdef _WIN32
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            return 0;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        if (!hUrl) {
            InternetCloseHandle(hInternet);
            return 0;
        }
        
        DWORD content_length = 0;
        DWORD buffer_size = sizeof(content_length);
        DWORD index = 0;
        
        HttpQueryInfoA(hUrl, HTTP_QUERY_CONTENT_LENGTH | HTTP_QUERY_FLAG_NUMBER,
                      &content_length, &buffer_size, &index);
        
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        return content_length;
#else
        return 0; // Not implemented for non-Windows
#endif
    }
};

} // namespace sco
