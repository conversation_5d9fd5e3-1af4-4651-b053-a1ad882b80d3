#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include "../utils/output.hpp"
#include "config.hpp"
#include "manifest.hpp"
#include "dependency_resolver.hpp"
#include "../utils/download_manager.hpp"
#include "../utils/extractor.hpp"
#include "../utils/shim_manager.hpp"

namespace sco {

struct InstallationInfo {
    std::string app_name;
    std::string version;
    std::string bucket;
    std::filesystem::path install_path;
    std::chrono::system_clock::time_point install_time;
    std::vector<std::string> installed_files;
    std::vector<std::string> created_shims;
};

class InstallManager {
public:
    struct InstallOptions {
        bool global = false;
        bool skip_dependencies = false;
        bool force_reinstall = false;
        bool no_cache = false;
        std::string architecture = "64bit";
    };
    
    struct InstallResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> installed_apps;
        std::vector<std::string> failed_apps;
        std::chrono::milliseconds total_duration{0};
    };
    
    static InstallResult install_apps(const std::vector<std::string>& app_names, 
                                    const InstallOptions& options = {}) {
        InstallManager manager(options);
        return manager.perform_installation(app_names);
    }
    
private:
    InstallOptions options_;
    Config& config_;
    
    explicit InstallManager(const InstallOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    InstallResult perform_installation(const std::vector<std::string>& app_names) {
        InstallResult result;
        auto start_time = std::chrono::steady_clock::now();

        SPDLOG_INFO("Starting installation of {} app(s)", app_names.size());

        try {
            // Ensure 7zip is available for extraction
            if (!ensure_7zip_available()) {
                result.error_message = "Failed to ensure 7zip is available for extraction";
                return result;
            }
            // Step 1: Resolve dependencies
            std::vector<std::string> install_order;
            if (!options_.skip_dependencies) {
                auto resolve_result = DependencyResolver::resolve(app_names);
                if (!resolve_result.success) {
                    result.error_message = "Dependency resolution failed";
                    if (!resolve_result.circular_dependencies.empty()) {
                        result.error_message += ": Circular dependencies detected";
                    }
                    if (!resolve_result.missing_dependencies.empty()) {
                        result.error_message += ": Missing dependencies";
                    }
                    return result;
                }
                install_order = resolve_result.install_order;
            } else {
                install_order = app_names;
            }
            
            // Step 2: Filter out already installed apps (unless force reinstall)
            if (!options_.force_reinstall) {
                install_order = DependencyResolver::filter_installed_apps(install_order);
            }
            
            if (install_order.empty()) {
                output::info("All requested apps are already installed");
                result.success = true;
                return result;
            }

            output::info("Will install {} app(s) in order: {}", install_order.size(),
                       join_strings(install_order, ", "));
            
            // Step 3: Install each app in order
            for (const auto& app_name : install_order) {
                if (install_single_app(app_name)) {
                    result.installed_apps.push_back(app_name);
                    output::info("Successfully installed: {}", app_name);
                } else {
                    result.failed_apps.push_back(app_name);
                    output::error("Failed to install: {}", app_name);
                    
                    // Decide whether to continue or abort
                    if (std::find(app_names.begin(), app_names.end(), app_name) != app_names.end()) {
                        // This was a requested app, not a dependency - abort
                        result.error_message = "Failed to install requested app: " + app_name;
                        break;
                    }
                    // This was a dependency - continue but log the failure
                }
            }
            
            result.success = result.failed_apps.empty() || 
                           (!result.installed_apps.empty() && result.failed_apps.size() < install_order.size());
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            output::error("Installation failed with exception: {}", e.what());
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        output::info("Installation completed in {}ms. Success: {}, Installed: {}, Failed: {}",
                   result.total_duration.count(), result.success,
                   result.installed_apps.size(), result.failed_apps.size());
        
        return result;
    }
    
    bool install_single_app(const std::string& app_name) {
        output::info("Installing app: {}", app_name);
        
        try {
            // Step 1: Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                SPDLOG_ERROR("Could not find valid manifest for: {}", app_name);
                return false;
            }
            
            SPDLOG_INFO("Found manifest: {} v{} from bucket '{}'", 
                       manifest.name, manifest.version, manifest.bucket);
            
            // Step 2: Prepare installation directory
            auto app_dir = prepare_app_directory(manifest);
            if (app_dir.empty()) {
                SPDLOG_ERROR("Failed to prepare app directory for: {}", app_name);
                return false;
            }
            
            // Step 3: Download files
            auto urls = manifest.get_urls(options_.architecture);
            if (urls.empty()) {
                SPDLOG_ERROR("No download URLs found for: {}", app_name);
                return false;
            }
            
            std::vector<std::filesystem::path> downloaded_files;
            for (const auto& url_info : urls) {
                auto downloaded_file = download_file(url_info, app_dir, manifest.name, manifest.version);
                if (downloaded_file.empty()) {
                    SPDLOG_ERROR("Failed to download file for: {}", app_name);
                    return false;
                }
                downloaded_files.push_back(downloaded_file);
            }
            
            // Step 4: Extract files
            for (const auto& downloaded_file : downloaded_files) {
                if (!extract_file(downloaded_file, app_dir, manifest)) {
                    SPDLOG_ERROR("Failed to extract file for: {}", app_name);
                    return false;
                }
            }
            
            // Step 5: Run pre-install script
            auto pre_install_script = manifest.get_pre_install(options_.architecture);
            if (!pre_install_script.empty()) {
                if (!run_script(pre_install_script, app_dir, "pre-install")) {
                    SPDLOG_WARN("Pre-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }
            
            // Step 6: Create shims
            auto bin_entries = manifest.get_bin(options_.architecture);
            if (!bin_entries.empty()) {
                if (!ShimManager::create_shims_for_app(app_name, app_dir, bin_entries)) {
                    SPDLOG_WARN("Failed to create some shims for: {}", app_name);
                    // Don't fail installation for shim creation failures
                }
            }
            
            // Step 7: Run post-install script
            auto post_install_script = manifest.get_post_install(options_.architecture);
            if (!post_install_script.empty()) {
                if (!run_script(post_install_script, app_dir, "post-install")) {
                    SPDLOG_WARN("Post-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }
            
            // Step 8: Save installation info
            if (!save_installation_info(manifest, app_dir)) {
                SPDLOG_WARN("Failed to save installation info for: {}", app_name);
                // Don't fail installation for this
            }
            
            // Step 9: Clean up downloaded files if not caching
            if (options_.no_cache) {
                for (const auto& downloaded_file : downloaded_files) {
                    try {
                        std::filesystem::remove(downloaded_file);
                    } catch (const std::exception& e) {
                        output::warn("Failed to clean up downloaded file {}: {}",
                                   downloaded_file.string(), e.what());
                    }
                }
            }
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Exception during installation of {}: {}", app_name, e.what());
            return false;
        }
    }
    
    std::filesystem::path prepare_app_directory(const Manifest& manifest) {
        auto apps_dir = config_.get_apps_dir();
        auto app_dir = apps_dir / manifest.name;
        auto version_dir = app_dir / manifest.version;
        auto current_dir = app_dir / "current";
        
        try {
            // Create version-specific directory
            std::filesystem::create_directories(version_dir);
            
            // Remove existing "current" symlink/directory
            if (std::filesystem::exists(current_dir)) {
                std::filesystem::remove_all(current_dir);
            }
            
            // Create "current" symlink to version directory
#ifdef _WIN32
            // On Windows, create a junction point
            std::string command = "mklink /J \"" + current_dir.string() + "\" \"" + version_dir.string() + "\"";
            int result = system(command.c_str());
            if (result != 0) {
                SPDLOG_WARN("Failed to create junction, using directory copy instead");
                std::filesystem::create_directories(current_dir);
            }
#else
            // On Unix-like systems, create a symbolic link
            std::filesystem::create_symlink(version_dir, current_dir);
#endif
            
            return version_dir;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to prepare app directory: {}", e.what());
            return {};
        }
    }
    
    std::filesystem::path download_file(const ManifestUrl& url_info,
                                      const std::filesystem::path& app_dir,
                                      const std::string& app_name,
                                      const std::string& version) {
        auto cache_dir = config_.get_cache_dir();
        std::filesystem::create_directories(cache_dir);

        // Generate Scoop-compatible filename: app#version#hash.ext
        std::string filename = DownloadManager::generate_cache_filename(
            app_name, version, url_info.url, url_info.hash);

        auto cache_file = cache_dir / filename;
        
        // Check if file already exists in cache and hash matches
        if (std::filesystem::exists(cache_file) && !options_.no_cache) {
            if (DownloadManager::verify_hash(cache_file, url_info.hash)) {
                SPDLOG_INFO("Using cached file: {}", cache_file.string());
                return cache_file;
            } else {
                SPDLOG_WARN("Cached file hash mismatch, re-downloading");
                std::filesystem::remove(cache_file);
            }
        }
        
        // Download file
        SPDLOG_INFO("Downloading: {}", url_info.url);
        auto result = DownloadManager::download_with_progress(url_info.url, cache_file);
        
        if (!result.success) {
            SPDLOG_ERROR("Download failed: {}", result.error_message);
            return {};
        }
        
        // Verify hash
        if (!DownloadManager::verify_hash(cache_file, url_info.hash)) {
            SPDLOG_ERROR("Downloaded file hash verification failed");
            std::filesystem::remove(cache_file);
            return {};
        }
        
        return cache_file;
    }
    
    bool extract_file(const std::filesystem::path& archive_path,
                     const std::filesystem::path& app_dir,
                     const Manifest& manifest) {
        
        if (!Extractor::is_extractable(archive_path)) {
            // File is not an archive, just copy it
            try {
                auto dest_file = app_dir / archive_path.filename();
                std::filesystem::copy_file(archive_path, dest_file);
                return true;
            } catch (const std::exception& e) {
                SPDLOG_ERROR("Failed to copy file: {}", e.what());
                return false;
            }
        }
        
        auto extract_dir = manifest.get_architecture(options_.architecture) ? 
                          manifest.get_architecture(options_.architecture)->extract_dir : 
                          manifest.extract_dir;
        auto extract_to = manifest.get_architecture(options_.architecture) ? 
                         manifest.get_architecture(options_.architecture)->extract_to : 
                         manifest.extract_to;
        
        auto result = Extractor::extract_archive(archive_path, app_dir, extract_dir, extract_to);
        
        if (!result.success) {
            SPDLOG_ERROR("Extraction failed: {}", result.error_message);
            return false;
        }
        
        return true;
    }
    
    bool run_script(const std::string& script, 
                   const std::filesystem::path& app_dir,
                   const std::string& script_type) {
        SPDLOG_INFO("Running {} script", script_type);
        
        try {
            // Create temporary script file
            auto temp_script = app_dir / ("temp_" + script_type + ".ps1");
            
            std::ofstream script_file(temp_script);
            if (!script_file.is_open()) {
                SPDLOG_ERROR("Failed to create temporary script file");
                return false;
            }
            
            script_file << script;
            script_file.close();
            
            // Execute script
            std::string command = "powershell.exe -ExecutionPolicy Bypass -File \"" + 
                                temp_script.string() + "\"";
            
            SPDLOG_DEBUG("Executing script: {}", command);
            
            int exit_code = system(command.c_str());
            
            // Clean up temporary script
            std::filesystem::remove(temp_script);
            
            if (exit_code == 0) {
                SPDLOG_INFO("{} script completed successfully", script_type);
                return true;
            } else {
                SPDLOG_ERROR("{} script failed with exit code: {}", script_type, exit_code);
                return false;
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to run {} script: {}", script_type, e.what());
            return false;
        }
    }
    
    bool save_installation_info(const Manifest& manifest, 
                              const std::filesystem::path& app_dir) {
        try {
            InstallationInfo info;
            info.app_name = manifest.name;
            info.version = manifest.version;
            info.bucket = manifest.bucket;
            info.install_path = app_dir;
            info.install_time = std::chrono::system_clock::now();
            
            // Save to JSON file
            auto info_file = app_dir / "scoop-install.json";
            std::ofstream file(info_file);
            if (!file.is_open()) {
                return false;
            }
            
            nlohmann::json json;
            json["app_name"] = info.app_name;
            json["version"] = info.version;
            json["bucket"] = info.bucket;
            json["install_path"] = info.install_path.string();
            json["install_time"] = std::chrono::duration_cast<std::chrono::seconds>(
                info.install_time.time_since_epoch()).count();
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Failed to save installation info: {}", e.what());
            return false;
        }
    }
    
    bool ensure_7zip_available() {
        // Check if 7zip is already available
        if (Extractor::is_7zip_available()) {
            output::debug("7zip is already available");
            return true;
        }

        // Check if 7zip is installed via Scoop but not in PATH
        if (DependencyResolver::is_app_installed("7zip")) {
            output::debug("7zip is installed via Scoop");
            return true;
        }

        SPDLOG_INFO("7zip not found, installing it first...");

        // Install 7zip first (without dependencies to avoid recursion)
        InstallOptions seven_zip_options = options_;
        seven_zip_options.skip_dependencies = true;

        // Temporarily create a new manager to avoid recursion
        InstallManager seven_zip_manager(seven_zip_options);
        auto seven_zip_result = seven_zip_manager.install_single_app("7zip");

        if (!seven_zip_result) {
            SPDLOG_ERROR("Failed to install 7zip");
            return false;
        }

        SPDLOG_INFO("7zip installed successfully");
        return true;
    }

    template<typename Container>
    std::string join_strings(const Container& container, const std::string& delimiter) {
        if (container.empty()) return "";

        std::ostringstream oss;
        auto it = container.begin();
        oss << *it;
        ++it;

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }
};

} // namespace sco
