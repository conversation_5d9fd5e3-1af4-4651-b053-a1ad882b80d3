#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <spdlog/spdlog.h>
#include "config.hpp"
#include "manifest.hpp"
#include "install_manager.hpp"
#include "uninstall_manager.hpp"

namespace sco {

class UpdateManager {
public:
    struct UpdateOptions {
        bool global = false;
        bool force = false;
        bool no_cache = false;
        bool skip_dependencies = false;
    };
    
    struct UpdateInfo {
        std::string name;
        std::string current_version;
        std::string latest_version;
        std::string bucket;
    };
    
    struct UpdateResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> updated_apps;
        std::vector<std::string> failed_apps;
        std::vector<std::string> skipped_apps;
        std::chrono::milliseconds total_duration{0};
    };
    
    static UpdateResult update_apps(const std::vector<UpdateInfo>& updates, 
                                  const UpdateOptions& options = {}) {
        UpdateManager manager(options);
        return manager.perform_updates(updates);
    }
    
private:
    UpdateOptions options_;
    Config& config_;
    
    explicit UpdateManager(const UpdateOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    UpdateResult perform_updates(const std::vector<UpdateInfo>& updates) {
        UpdateResult result;
        auto start_time = std::chrono::steady_clock::now();

        SPDLOG_INFO("Starting update of {} app(s)", updates.size());

        try {
            for (const auto& update : updates) {
                if (update_single_app(update)) {
                    result.updated_apps.push_back(update.name);
                    SPDLOG_INFO("Successfully updated: {} from {} to {}", 
                               update.name, update.current_version, update.latest_version);
                } else {
                    result.failed_apps.push_back(update.name);
                    SPDLOG_ERROR("Failed to update: {}", update.name);
                }
            }
            
            result.success = result.failed_apps.empty() || !result.updated_apps.empty();
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            SPDLOG_ERROR("Update failed with exception: {}", e.what());
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        SPDLOG_INFO("Update completed in {}ms. Success: {}, Updated: {}, Failed: {}",
                   result.total_duration.count(), result.success, 
                   result.updated_apps.size(), result.failed_apps.size());
        
        return result;
    }
    
    bool update_single_app(const UpdateInfo& update) {
        SPDLOG_INFO("Updating app: {} from {} to {}", 
                   update.name, update.current_version, update.latest_version);
        
        try {
            // Step 1: Backup current installation info
            if (!backup_current_installation(update.name)) {
                SPDLOG_WARN("Failed to backup current installation for: {}", update.name);
                // Continue anyway
            }
            
            // Step 2: Prepare install options
            InstallManager::InstallOptions install_options;
            install_options.global = options_.global;
            install_options.force_reinstall = true; // Force reinstall for updates
            install_options.no_cache = options_.no_cache;
            install_options.skip_dependencies = options_.skip_dependencies;
            
            // Step 3: Install the new version
            auto install_result = InstallManager::install_apps({update.name}, install_options);
            
            if (!install_result.success) {
                SPDLOG_ERROR("Failed to install new version of {}: {}", 
                           update.name, install_result.error_message);
                
                // Try to restore backup
                restore_backup(update.name);
                return false;
            }
            
            // Step 4: Clean up old versions (keep the previous one as backup)
            cleanup_old_versions(update.name, update.latest_version);
            
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Exception during update of {}: {}", update.name, e.what());
            restore_backup(update.name);
            return false;
        }
    }
    
    bool backup_current_installation(const std::string& app_name) {
        auto apps_dir = config_.get_apps_dir();
        auto app_dir = apps_dir / app_name;
        auto current_dir = app_dir / "current";
        auto backup_dir = app_dir / "backup";
        
        if (!std::filesystem::exists(current_dir)) {
            SPDLOG_DEBUG("No current installation to backup for: {}", app_name);
            return true;
        }
        
        try {
            // Remove existing backup
            if (std::filesystem::exists(backup_dir)) {
                std::filesystem::remove_all(backup_dir);
            }
            
            // Copy current to backup
            std::filesystem::copy(current_dir, backup_dir, 
                                std::filesystem::copy_options::recursive);
            
            SPDLOG_DEBUG("Backed up current installation for: {}", app_name);
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to backup installation for {}: {}", app_name, e.what());
            return false;
        }
    }
    
    bool restore_backup(const std::string& app_name) {
        auto apps_dir = config_.get_apps_dir();
        auto app_dir = apps_dir / app_name;
        auto current_dir = app_dir / "current";
        auto backup_dir = app_dir / "backup";
        
        if (!std::filesystem::exists(backup_dir)) {
            SPDLOG_DEBUG("No backup to restore for: {}", app_name);
            return false;
        }
        
        try {
            // Remove failed current installation
            if (std::filesystem::exists(current_dir)) {
                std::filesystem::remove_all(current_dir);
            }
            
            // Restore from backup
            std::filesystem::copy(backup_dir, current_dir, 
                                std::filesystem::copy_options::recursive);
            
            SPDLOG_INFO("Restored backup for: {}", app_name);
            return true;
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to restore backup for {}: {}", app_name, e.what());
            return false;
        }
    }
    
    void cleanup_old_versions(const std::string& app_name, const std::string& current_version) {
        auto apps_dir = config_.get_apps_dir();
        auto app_dir = apps_dir / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return;
        }
        
        try {
            std::vector<std::string> versions;
            
            // Collect all version directories
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "backup") {
                        versions.push_back(dir_name);
                    }
                }
            }
            
            // Keep only the current version and one previous version
            if (versions.size() > 2) {
                // Sort versions (simple string sort, could be improved with proper version comparison)
                std::sort(versions.begin(), versions.end());
                
                // Remove older versions (keep last 2)
                for (size_t i = 0; i < versions.size() - 2; ++i) {
                    if (versions[i] != current_version) {
                        auto old_version_dir = app_dir / versions[i];
                        try {
                            std::filesystem::remove_all(old_version_dir);
                            SPDLOG_DEBUG("Removed old version: {} {}", app_name, versions[i]);
                        } catch (const std::exception& e) {
                            SPDLOG_WARN("Failed to remove old version {} {}: {}", 
                                       app_name, versions[i], e.what());
                        }
                    }
                }
            }
            
            // Clean up backup directory
            auto backup_dir = app_dir / "backup";
            if (std::filesystem::exists(backup_dir)) {
                try {
                    std::filesystem::remove_all(backup_dir);
                    SPDLOG_DEBUG("Cleaned up backup for: {}", app_name);
                } catch (const std::exception& e) {
                    SPDLOG_WARN("Failed to clean up backup for {}: {}", app_name, e.what());
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_WARN("Failed to cleanup old versions for {}: {}", app_name, e.what());
        }
    }
};

} // namespace sco
