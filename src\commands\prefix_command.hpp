#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include "../utils/output.hpp"

namespace sco {

class PrefixCommand : public BaseCommand {
public:
    PrefixCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco prefix <app_name>\n";
                return 1;
            }

            SPDLOG_DEBUG("Getting prefix for app: {}", app_name_);

            return show_app_prefix();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Prefix command failed: {}", e.what());
            std::cerr << "Failed to get app prefix: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "prefix"; }
    std::string get_description() const override { return "Returns the path to the specified app"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    
private:
    std::string app_name_;
    
    int show_app_prefix() {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name_;
        
        // Check if app is installed
        if (!std::filesystem::exists(app_dir)) {
            std::cout << "App '" << app_name_ << "' is not installed.\n";
            std::cout << "Run 'sco install " << app_name_ << "' to install it.\n";
            return 1;
        }
        
        // Check for current directory (symlink/junction to active version)
        auto current_dir = app_dir / "current";
        if (std::filesystem::exists(current_dir)) {
            // Output the current directory path
            std::cout << current_dir.string() << std::endl;
            return 0;
        }
        
        // If no current directory, just output the app directory
        std::cout << app_dir.string() << std::endl;
        return 0;
    }
};

} // namespace sco
