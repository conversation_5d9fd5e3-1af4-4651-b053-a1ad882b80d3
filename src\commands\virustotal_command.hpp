#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <sstream>
#include "../utils/output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#endif

namespace sco {

class VirusTotalCommand : public BaseCommand {
public:
    VirusTotalCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                std::cerr << "App name(s) required.\n";
                std::cout << "Usage: sco virustotal <app1> [app2] ...\n";
                return 1;
            }

            SPDLOG_DEBUG("VirusTotal command called for {} app(s)", app_names_.size());

            return check_apps();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("VirusTotal command failed: {}", e.what());
            std::cerr << "VirusTotal check failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "virustotal"; }
    std::string get_description() const override { return "Look for app's hash or url on virustotal.com"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    void set_architecture(const std::string& arch) { architecture_ = arch; }
    void set_no_browser(bool no_browser) { no_browser_ = no_browser; }
    
private:
    std::vector<std::string> app_names_;
    std::string architecture_ = "64bit";
    bool no_browser_ = false;
    
    struct VirusTotalInfo {
        std::string app_name;
        std::vector<std::string> urls;
        std::vector<std::string> hashes;
        bool found_manifest = false;
    };
    
    int check_apps() {
        std::cout << "Checking apps on VirusTotal...\n\n";
        
        std::vector<VirusTotalInfo> check_list;
        
        // Prepare check list
        for (const auto& app_name : app_names_) {
            VirusTotalInfo info;
            info.app_name = app_name;
            
            // Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                std::cout << "✗ " << app_name << ": Manifest not found\n";
                continue;
            }
            
            info.found_manifest = true;
            
            // Get URLs and hashes for the specified architecture
            auto url_infos = manifest.get_urls(architecture_);
            for (const auto& url_info : url_infos) {
                info.urls.push_back(url_info.url);
                if (!url_info.hash.empty()) {
                    info.hashes.push_back(url_info.hash);
                }
            }
            
            check_list.push_back(info);
        }
        
        if (check_list.empty()) {
            std::cout << "No valid apps found to check.\n";
            return 1;
        }
        
        // Show summary
        show_check_summary(check_list);
        
        // Open VirusTotal pages
        if (!no_browser_) {
            for (const auto& info : check_list) {
                if (!info.found_manifest) continue;
                
                std::cout << "\nOpening VirusTotal for " << info.app_name << "...\n";
                
                // Check hashes first (more reliable)
                for (const auto& hash : info.hashes) {
                    std::string vt_url = "https://www.virustotal.com/gui/file/" + hash;
                    std::cout << "  Hash: " << hash.substr(0, 16) << "...\n";
                    std::cout << "  URL: " << vt_url << "\n";
                    
                    if (!open_url_in_browser(vt_url)) {
                        std::cout << "  Failed to open browser\n";
                    }
                }
                
                // If no hashes, check URLs
                if (info.hashes.empty()) {
                    for (const auto& url : info.urls) {
                        std::string encoded_url = url_encode(url);
                        std::string vt_url = "https://www.virustotal.com/gui/url/" + encoded_url;
                        std::cout << "  URL: " << url << "\n";
                        std::cout << "  VirusTotal: " << vt_url << "\n";
                        
                        if (!open_url_in_browser(vt_url)) {
                            std::cout << "  Failed to open browser\n";
                        }
                    }
                }
            }
        } else {
            std::cout << "\nVirusTotal URLs (use --no-browser to suppress auto-opening):\n";
            for (const auto& info : check_list) {
                if (!info.found_manifest) continue;
                
                std::cout << "\n" << info.app_name << ":\n";
                
                for (const auto& hash : info.hashes) {
                    std::cout << "  Hash: https://www.virustotal.com/gui/file/" << hash << "\n";
                }
                
                if (info.hashes.empty()) {
                    for (const auto& url : info.urls) {
                        std::string encoded_url = url_encode(url);
                        std::cout << "  URL: https://www.virustotal.com/gui/url/" << encoded_url << "\n";
                    }
                }
            }
        }
        
        return 0;
    }
    
    void show_check_summary(const std::vector<VirusTotalInfo>& check_list) {
        std::cout << "VirusTotal Check Summary:\n";
        std::cout << "========================\n";
        
        TableFormatter table;
        table.add_column("App", true);
        table.add_column("URLs", true);
        table.add_column("Hashes", true);
        table.add_column("Architecture", true);
        
        for (const auto& info : check_list) {
            if (!info.found_manifest) continue;
            
            table.add_row({
                info.app_name,
                std::to_string(info.urls.size()),
                std::to_string(info.hashes.size()),
                architecture_
            });
        }
        
        table.print();
        std::cout << "\n";
    }
    
    bool open_url_in_browser(const std::string& url) {
        try {
#ifdef _WIN32
            // Windows: Use ShellExecute
            HINSTANCE result = ShellExecuteA(
                NULL,           // Parent window
                "open",         // Operation
                url.c_str(),    // File/URL to open
                NULL,           // Parameters
                NULL,           // Working directory
                SW_SHOWNORMAL   // Show command
            );
            
            return reinterpret_cast<intptr_t>(result) > 32;
            
#elif defined(__APPLE__)
            // macOS: Use 'open' command
            std::string command = "open \"" + url + "\"";
            return std::system(command.c_str()) == 0;
            
#else
            // Linux: Try common browser commands
            std::vector<std::string> browsers = {
                "xdg-open", "firefox", "chromium", "google-chrome"
            };
            
            for (const auto& browser : browsers) {
                std::string command = browser + " \"" + url + "\" 2>/dev/null";
                if (std::system(command.c_str()) == 0) {
                    return true;
                }
            }
            
            return false;
#endif
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Exception while opening URL {}: {}", url, e.what());
            return false;
        }
    }
    
    std::string url_encode(const std::string& url) {
        std::ostringstream encoded;
        encoded << std::hex << std::uppercase;
        
        for (char c : url) {
            if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
                encoded << c;
            } else {
                encoded << '%' << std::setw(2) << std::setfill('0') << static_cast<unsigned char>(c);
            }
        }
        
        return encoded.str();
    }
};

} // namespace sco
