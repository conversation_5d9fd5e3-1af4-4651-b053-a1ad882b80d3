#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include "config.hpp"

namespace sco {

struct ManifestUrl {
    std::string url;
    std::string hash;
    std::string extract_dir;
    std::string extract_to;
};

struct ManifestArchitecture {
    std::vector<ManifestUrl> urls;
    std::vector<std::string> bin;
    std::vector<std::string> shortcuts;
    std::unordered_map<std::string, std::string> env_add_path;
    std::unordered_map<std::string, std::string> env_set;
    std::string hash;
    std::string extract_dir;
    std::string extract_to;
    std::string pre_install;
    std::string post_install;
    std::string pre_uninstall;
    std::string post_uninstall;
};

struct Manifest {
    std::string name;
    std::string version;
    std::string description;
    std::string homepage;
    std::string license;
    std::vector<std::string> depends;
    std::vector<std::string> suggest;
    
    // Architecture-specific data
    std::unordered_map<std::string, ManifestArchitecture> architecture;
    
    // Default architecture data (when no architecture is specified)
    std::vector<ManifestUrl> urls;
    std::vector<std::string> bin;
    std::vector<std::string> shortcuts;
    std::unordered_map<std::string, std::string> env_add_path;
    std::unordered_map<std::string, std::string> env_set;
    std::string hash;
    std::string extract_dir;
    std::string extract_to;
    std::string pre_install;
    std::string post_install;
    std::string pre_uninstall;
    std::string post_uninstall;
    
    // Installation metadata
    std::string bucket;
    std::filesystem::path manifest_path;
    
    bool is_valid() const {
        return !name.empty() && !version.empty() && (!urls.empty() || !architecture.empty());
    }
    
    // Get architecture-specific data or fall back to default
    const ManifestArchitecture* get_architecture(const std::string& arch) const {
        auto it = architecture.find(arch);
        if (it != architecture.end()) {
            return &it->second;
        }
        return nullptr;
    }
    
    // Get URLs for specific architecture or default
    std::vector<ManifestUrl> get_urls(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->urls.empty()) {
            return arch_data->urls;
        }
        return urls;
    }
    
    // Get bin entries for specific architecture or default
    std::vector<std::string> get_bin(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->bin.empty()) {
            return arch_data->bin;
        }
        return bin;
    }
    
    // Get environment variables for specific architecture or default
    std::unordered_map<std::string, std::string> get_env_add_path(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->env_add_path.empty()) {
            return arch_data->env_add_path;
        }
        return env_add_path;
    }
    
    std::unordered_map<std::string, std::string> get_env_set(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->env_set.empty()) {
            return arch_data->env_set;
        }
        return env_set;
    }
    
    // Get scripts for specific architecture or default
    std::string get_pre_install(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->pre_install.empty()) {
            return arch_data->pre_install;
        }
        return pre_install;
    }
    
    std::string get_post_install(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->post_install.empty()) {
            return arch_data->post_install;
        }
        return post_install;
    }
    
    std::string get_pre_uninstall(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->pre_uninstall.empty()) {
            return arch_data->pre_uninstall;
        }
        return pre_uninstall;
    }
    
    std::string get_post_uninstall(const std::string& arch = "64bit") const {
        const auto* arch_data = get_architecture(arch);
        if (arch_data && !arch_data->post_uninstall.empty()) {
            return arch_data->post_uninstall;
        }
        return post_uninstall;
    }
};

class ManifestParser {
public:
    static Manifest parse_file(const std::filesystem::path& manifest_path) {
        Manifest manifest;
        manifest.manifest_path = manifest_path;
        manifest.name = manifest_path.stem().string();
        
        try {
            std::ifstream file(manifest_path);
            if (!file.is_open()) {
                SPDLOG_ERROR("Could not open manifest file: {}", manifest_path.string());
                return manifest;
            }
            
            nlohmann::json json;
            file >> json;
            
            return parse_json(json, manifest);
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Error parsing manifest {}: {}", manifest_path.string(), e.what());
            return manifest;
        }
    }
    
    static Manifest find_and_parse(const std::string& app_name, const std::string& bucket_name = "") {
        auto& config = Config::instance();
        config.load();
        auto buckets_dir = config.get_buckets_dir();

        if (!std::filesystem::exists(buckets_dir)) {
            SPDLOG_ERROR("Buckets directory does not exist: {}", buckets_dir.string());
            Manifest manifest;
            manifest.name = app_name;
            return manifest;
        }

        // If bucket name is specified, search only in that bucket
        if (!bucket_name.empty()) {
            auto bucket_path = buckets_dir / bucket_name;
            auto manifest = search_in_bucket(bucket_path, app_name);
            if (manifest.is_valid()) {
                manifest.bucket = bucket_name;
                return manifest;
            }
        } else {
            // Search in all buckets
            try {
                for (const auto& bucket_entry : std::filesystem::directory_iterator(buckets_dir)) {
                    if (bucket_entry.is_directory()) {
                        std::string current_bucket = bucket_entry.path().filename().string();
                        auto manifest = search_in_bucket(bucket_entry.path(), app_name);
                        if (manifest.is_valid()) {
                            manifest.bucket = current_bucket;
                            return manifest;
                        }
                    }
                }
            } catch (const std::filesystem::filesystem_error& e) {
                SPDLOG_ERROR("Error searching buckets: {}", e.what());
            }
        }

        // Not found
        Manifest manifest;
        manifest.name = app_name;
        return manifest;
    }

private:
    static Manifest search_in_bucket(const std::filesystem::path& bucket_path, const std::string& app_name) {
        auto manifests_dir = bucket_path / "bucket";
        if (!std::filesystem::exists(manifests_dir)) {
            // Try alternative structure
            manifests_dir = bucket_path;
        }

        if (!std::filesystem::exists(manifests_dir)) {
            Manifest manifest;
            manifest.name = app_name;
            return manifest;
        }

        auto manifest_file = manifests_dir / (app_name + ".json");
        if (std::filesystem::exists(manifest_file)) {
            return parse_file(manifest_file);
        }

        // Not found
        Manifest manifest;
        manifest.name = app_name;
        return manifest;
    }

private:
    static Manifest parse_json(const nlohmann::json& json, Manifest& manifest) {
        // Parse basic fields
        if (json.contains("version")) {
            manifest.version = json["version"].get<std::string>();
        }
        
        if (json.contains("description")) {
            manifest.description = json["description"].get<std::string>();
        }
        
        if (json.contains("homepage")) {
            manifest.homepage = json["homepage"].get<std::string>();
        }
        
        if (json.contains("license")) {
            if (json["license"].is_string()) {
                manifest.license = json["license"].get<std::string>();
            } else if (json["license"].is_object() && json["license"].contains("identifier")) {
                manifest.license = json["license"]["identifier"].get<std::string>();
            }
        }
        
        // Parse dependencies
        if (json.contains("depends")) {
            if (json["depends"].is_array()) {
                for (const auto& dep : json["depends"]) {
                    manifest.depends.push_back(dep.get<std::string>());
                }
            } else if (json["depends"].is_string()) {
                manifest.depends.push_back(json["depends"].get<std::string>());
            }
        }
        
        if (json.contains("suggest")) {
            if (json["suggest"].is_array()) {
                for (const auto& sug : json["suggest"]) {
                    manifest.suggest.push_back(sug.get<std::string>());
                }
            } else if (json["suggest"].is_string()) {
                manifest.suggest.push_back(json["suggest"].get<std::string>());
            }
        }
        
        // Parse URLs
        parse_urls(json, manifest);
        
        // Parse bin entries
        parse_bin(json, manifest);
        
        // Parse environment variables
        parse_env(json, manifest);
        
        // Parse scripts
        parse_scripts(json, manifest);
        
        // Parse architecture-specific data
        if (json.contains("architecture")) {
            parse_architecture(json["architecture"], manifest);
        }
        
        return manifest;
    }
    
    static void parse_urls(const nlohmann::json& json, Manifest& manifest) {
        if (json.contains("url")) {
            if (json["url"].is_array()) {
                for (const auto& url_item : json["url"]) {
                    ManifestUrl url;
                    url.url = url_item.get<std::string>();
                    manifest.urls.push_back(url);
                }
            } else if (json["url"].is_string()) {
                ManifestUrl url;
                url.url = json["url"].get<std::string>();
                manifest.urls.push_back(url);
            }
        }
        
        if (json.contains("hash") && !manifest.urls.empty()) {
            if (json["hash"].is_array()) {
                for (size_t i = 0; i < manifest.urls.size() && i < json["hash"].size(); ++i) {
                    manifest.urls[i].hash = json["hash"][i].get<std::string>();
                }
            } else if (json["hash"].is_string()) {
                manifest.urls[0].hash = json["hash"].get<std::string>();
            }
        }
        
        if (json.contains("extract_dir")) {
            manifest.extract_dir = json["extract_dir"].get<std::string>();
        }
        
        if (json.contains("extract_to")) {
            manifest.extract_to = json["extract_to"].get<std::string>();
        }
    }
    
    static void parse_bin(const nlohmann::json& json, Manifest& manifest) {
        if (json.contains("bin")) {
            if (json["bin"].is_array()) {
                for (const auto& bin_item : json["bin"]) {
                    if (bin_item.is_string()) {
                        manifest.bin.push_back(bin_item.get<std::string>());
                    } else if (bin_item.is_array() && !bin_item.empty()) {
                        manifest.bin.push_back(bin_item[0].get<std::string>());
                    }
                }
            } else if (json["bin"].is_string()) {
                manifest.bin.push_back(json["bin"].get<std::string>());
            }
        }
    }
    
    static void parse_env(const nlohmann::json& json, Manifest& manifest) {
        if (json.contains("env_add_path")) {
            for (const auto& [key, value] : json["env_add_path"].items()) {
                manifest.env_add_path[key] = value.get<std::string>();
            }
        }
        
        if (json.contains("env_set")) {
            for (const auto& [key, value] : json["env_set"].items()) {
                manifest.env_set[key] = value.get<std::string>();
            }
        }
    }
    
    static void parse_scripts(const nlohmann::json& json, Manifest& manifest) {
        if (json.contains("pre_install")) {
            if (json["pre_install"].is_array()) {
                // Join array elements with newlines
                std::string script;
                for (const auto& line : json["pre_install"]) {
                    if (!script.empty()) script += "\n";
                    script += line.get<std::string>();
                }
                manifest.pre_install = script;
            } else if (json["pre_install"].is_string()) {
                manifest.pre_install = json["pre_install"].get<std::string>();
            }
        }
        
        if (json.contains("post_install")) {
            if (json["post_install"].is_array()) {
                std::string script;
                for (const auto& line : json["post_install"]) {
                    if (!script.empty()) script += "\n";
                    script += line.get<std::string>();
                }
                manifest.post_install = script;
            } else if (json["post_install"].is_string()) {
                manifest.post_install = json["post_install"].get<std::string>();
            }
        }
        
        if (json.contains("pre_uninstall")) {
            if (json["pre_uninstall"].is_array()) {
                std::string script;
                for (const auto& line : json["pre_uninstall"]) {
                    if (!script.empty()) script += "\n";
                    script += line.get<std::string>();
                }
                manifest.pre_uninstall = script;
            } else if (json["pre_uninstall"].is_string()) {
                manifest.pre_uninstall = json["pre_uninstall"].get<std::string>();
            }
        }
        
        if (json.contains("post_uninstall")) {
            if (json["post_uninstall"].is_array()) {
                std::string script;
                for (const auto& line : json["post_uninstall"]) {
                    if (!script.empty()) script += "\n";
                    script += line.get<std::string>();
                }
                manifest.post_uninstall = script;
            } else if (json["post_uninstall"].is_string()) {
                manifest.post_uninstall = json["post_uninstall"].get<std::string>();
            }
        }
    }
    
    static void parse_architecture(const nlohmann::json& arch_json, Manifest& manifest) {
        for (const auto& [arch_name, arch_data] : arch_json.items()) {
            ManifestArchitecture arch;
            
            // Parse URLs for this architecture
            if (arch_data.contains("url")) {
                if (arch_data["url"].is_array()) {
                    for (const auto& url_item : arch_data["url"]) {
                        ManifestUrl url;
                        url.url = url_item.get<std::string>();
                        arch.urls.push_back(url);
                    }
                } else if (arch_data["url"].is_string()) {
                    ManifestUrl url;
                    url.url = arch_data["url"].get<std::string>();
                    arch.urls.push_back(url);
                }
            }
            
            // Parse hash for this architecture
            if (arch_data.contains("hash") && !arch.urls.empty()) {
                if (arch_data["hash"].is_array()) {
                    for (size_t i = 0; i < arch.urls.size() && i < arch_data["hash"].size(); ++i) {
                        arch.urls[i].hash = arch_data["hash"][i].get<std::string>();
                    }
                } else if (arch_data["hash"].is_string()) {
                    arch.urls[0].hash = arch_data["hash"].get<std::string>();
                }
            }
            
            // Parse bin for this architecture
            if (arch_data.contains("bin")) {
                if (arch_data["bin"].is_array()) {
                    for (const auto& bin_item : arch_data["bin"]) {
                        if (bin_item.is_string()) {
                            arch.bin.push_back(bin_item.get<std::string>());
                        } else if (bin_item.is_array() && !bin_item.empty()) {
                            arch.bin.push_back(bin_item[0].get<std::string>());
                        }
                    }
                } else if (arch_data["bin"].is_string()) {
                    arch.bin.push_back(arch_data["bin"].get<std::string>());
                }
            }
            
            // Parse environment variables for this architecture
            if (arch_data.contains("env_add_path")) {
                for (const auto& [key, value] : arch_data["env_add_path"].items()) {
                    arch.env_add_path[key] = value.get<std::string>();
                }
            }
            
            if (arch_data.contains("env_set")) {
                for (const auto& [key, value] : arch_data["env_set"].items()) {
                    arch.env_set[key] = value.get<std::string>();
                }
            }
            
            // Parse scripts for this architecture
            if (arch_data.contains("pre_install")) {
                if (arch_data["pre_install"].is_array()) {
                    std::string script;
                    for (const auto& line : arch_data["pre_install"]) {
                        if (!script.empty()) script += "\n";
                        script += line.get<std::string>();
                    }
                    arch.pre_install = script;
                } else if (arch_data["pre_install"].is_string()) {
                    arch.pre_install = arch_data["pre_install"].get<std::string>();
                }
            }
            
            if (arch_data.contains("post_install")) {
                if (arch_data["post_install"].is_array()) {
                    std::string script;
                    for (const auto& line : arch_data["post_install"]) {
                        if (!script.empty()) script += "\n";
                        script += line.get<std::string>();
                    }
                    arch.post_install = script;
                } else if (arch_data["post_install"].is_string()) {
                    arch.post_install = arch_data["post_install"].get<std::string>();
                }
            }
            
            if (arch_data.contains("pre_uninstall")) {
                if (arch_data["pre_uninstall"].is_array()) {
                    std::string script;
                    for (const auto& line : arch_data["pre_uninstall"]) {
                        if (!script.empty()) script += "\n";
                        script += line.get<std::string>();
                    }
                    arch.pre_uninstall = script;
                } else if (arch_data["pre_uninstall"].is_string()) {
                    arch.pre_uninstall = arch_data["pre_uninstall"].get<std::string>();
                }
            }
            
            if (arch_data.contains("post_uninstall")) {
                if (arch_data["post_uninstall"].is_array()) {
                    std::string script;
                    for (const auto& line : arch_data["post_uninstall"]) {
                        if (!script.empty()) script += "\n";
                        script += line.get<std::string>();
                    }
                    arch.post_uninstall = script;
                } else if (arch_data["post_uninstall"].is_string()) {
                    arch.post_uninstall = arch_data["post_uninstall"].get<std::string>();
                }
            }
            
            // Parse other architecture-specific fields
            if (arch_data.contains("extract_dir")) {
                arch.extract_dir = arch_data["extract_dir"].get<std::string>();
            }
            
            if (arch_data.contains("extract_to")) {
                arch.extract_to = arch_data["extract_to"].get<std::string>();
            }
            
            manifest.architecture[arch_name] = arch;
        }
    }
};

} // namespace sco
