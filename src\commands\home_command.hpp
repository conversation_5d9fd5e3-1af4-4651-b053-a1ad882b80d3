#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <cstdlib>
#include "../utils/output.hpp"

#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#endif

namespace sco {

class HomeCommand : public BaseCommand {
public:
    HomeCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco home <app_name> [app_name2] ...\n";
                return 1;
            }

            SPDLOG_DEBUG("Opening homepage for {} app(s)", app_names_.size());

            bool all_success = true;
            std::vector<std::string> failed_apps;
            std::vector<std::string> opened_apps;

            for (const auto& app_name : app_names_) {
                if (open_app_homepage(app_name)) {
                    opened_apps.push_back(app_name);
                } else {
                    failed_apps.push_back(app_name);
                    all_success = false;
                }
            }

            // Show results
            if (!opened_apps.empty()) {
                std::cout << "Opened homepage(s) for:\n";
                for (const auto& app : opened_apps) {
                    std::cout << "  + " << app << "\n";
                }
            }

            if (!failed_apps.empty()) {
                std::cout << "Failed to open homepage(s) for:\n";
                for (const auto& app : failed_apps) {
                    std::cout << "  - " << app << "\n";
                }
            }

            return all_success ? 0 : 1;

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Home command failed: {}", e.what());
            std::cerr << "Failed to open homepage: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "home"; }
    std::string get_description() const override { return "Opens the app homepage"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    
private:
    std::vector<std::string> app_names_;
    
    bool open_app_homepage(const std::string& app_name) {
        SPDLOG_DEBUG("Opening homepage for app: {}", app_name);
        
        // Find and parse manifest
        auto manifest = ManifestParser::find_and_parse(app_name);
        if (!manifest.is_valid()) {
            std::cout << "App '" << app_name << "' not found.\n";
            return false;
        }
        
        if (manifest.homepage.empty()) {
            std::cout << "No homepage URL found for '" << app_name << "'.\n";
            return false;
        }
        
        std::cout << "Opening homepage for '" << app_name << "': " << manifest.homepage << "\n";
        
        return open_url_in_browser(manifest.homepage);
    }
    
    bool open_url_in_browser(const std::string& url) {
        try {
#ifdef _WIN32
            // Windows: Use ShellExecute
            HINSTANCE result = ShellExecuteA(
                NULL,           // Parent window
                "open",         // Operation
                url.c_str(),    // File/URL to open
                NULL,           // Parameters
                NULL,           // Working directory
                SW_SHOWNORMAL   // Show command
            );
            
            // ShellExecute returns a value greater than 32 on success
            if (reinterpret_cast<intptr_t>(result) > 32) {
                SPDLOG_DEBUG("Successfully opened URL in browser: {}", url);
                return true;
            } else {
                SPDLOG_ERROR("Failed to open URL in browser. Error code: {}", 
                           reinterpret_cast<intptr_t>(result));
                return false;
            }
            
#elif defined(__APPLE__)
            // macOS: Use 'open' command
            std::string command = "open \"" + url + "\"";
            int result = std::system(command.c_str());
            
            if (result == 0) {
                SPDLOG_DEBUG("Successfully opened URL in browser: {}", url);
                return true;
            } else {
                SPDLOG_ERROR("Failed to open URL in browser. Exit code: {}", result);
                return false;
            }
            
#else
            // Linux: Try common browser commands
            std::vector<std::string> browsers = {
                "xdg-open",
                "firefox",
                "chromium",
                "google-chrome",
                "opera",
                "konqueror"
            };
            
            for (const auto& browser : browsers) {
                std::string command = browser + " \"" + url + "\" 2>/dev/null";
                int result = std::system(command.c_str());
                
                if (result == 0) {
                    output::debug("Successfully opened URL with {}: {}", browser, url);
                    return true;
                }
            }

            output::error("Failed to find a suitable browser to open URL: {}", url);
            return false;
#endif

        } catch (const std::exception& e) {
            output::error("Exception while opening URL {}: {}", url, e.what());
            return false;
        }
    }
};

} // namespace sco
