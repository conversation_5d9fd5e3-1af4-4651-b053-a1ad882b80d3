#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../core/dependency_resolver.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <set>
#include <map>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class DependsCommand : public BaseCommand {
public:
    DependsCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco depends <app_name>\n";
                return 1;
            }

            output::debug("Getting dependencies for app: {}", app_name_);

            return show_dependencies();

        } catch (const std::exception& e) {
            output::error("Depends command failed: {}", e.what());
            std::cerr << "Failed to get dependencies: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "depends"; }
    std::string get_description() const override { return "List dependencies for an app"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    void set_show_tree(bool show_tree) { show_tree_ = show_tree; }
    
private:
    std::string app_name_;
    bool show_tree_ = false;
    
    int show_dependencies() {
        // Find and parse manifest
        auto manifest = ManifestParser::find_and_parse(app_name_);
        if (!manifest.is_valid()) {
            std::cout << "App '" << app_name_ << "' not found.\n";
            std::cout << "Try 'sco search " << app_name_ << "' to find similar apps.\n";
            return 1;
        }
        
        std::cout << "Dependencies for '" << app_name_ << "':\n\n";
        
        if (manifest.depends.empty()) {
            std::cout << "No dependencies.\n";
            return 0;
        }
        
        if (show_tree_) {
            return show_dependency_tree();
        } else {
            return show_dependency_list();
        }
    }
    
    int show_dependency_list() {
        // Use dependency resolver to get full dependency list
        auto resolve_result = DependencyResolver::resolve({app_name_});
        
        if (!resolve_result.success) {
            std::cout << "Failed to resolve dependencies.\n";
            
            if (!resolve_result.circular_dependencies.empty()) {
                std::cout << "\nCircular dependencies detected:\n";
                for (const auto& cycle : resolve_result.circular_dependencies) {
                    std::cout << "  " << cycle << "\n";
                }
            }
            
            if (!resolve_result.missing_dependencies.empty()) {
                std::cout << "\nMissing dependencies:\n";
                for (const auto& missing : resolve_result.missing_dependencies) {
                    std::cout << "  " << missing << "\n";
                }
            }
            
            return 1;
        }
        
        // Remove the app itself from the list
        std::vector<std::string> dependencies;
        for (const auto& dep : resolve_result.install_order) {
            if (dep != app_name_) {
                dependencies.push_back(dep);
            }
        }
        
        if (dependencies.empty()) {
            std::cout << "No dependencies.\n";
            return 0;
        }
        
        std::cout << "Install order:\n";
        
        TableFormatter table;
        table.add_column("#", true);
        table.add_column("Name", true);
        table.add_column("Status", true);
        table.add_column("Version", true);
        
        for (size_t i = 0; i < dependencies.size(); ++i) {
            const auto& dep = dependencies[i];
            auto status = get_app_status(dep);
            auto version = get_app_version(dep);
            
            table.add_row({
                std::to_string(i + 1),
                dep,
                status,
                version
            });
        }
        
        table.print();
        
        std::cout << "\nTotal dependencies: " << dependencies.size() << "\n";
        return 0;
    }
    
    int show_dependency_tree() {
        std::cout << "Dependency tree:\n\n";
        
        std::set<std::string> visited;
        show_dependency_tree_recursive(app_name_, 0, visited);
        
        return 0;
    }
    
    void show_dependency_tree_recursive(const std::string& app_name, int depth, std::set<std::string>& visited) {
        // Print indentation
        for (int i = 0; i < depth; ++i) {
            std::cout << "  ";
        }
        
        // Check for circular dependency
        if (visited.find(app_name) != visited.end()) {
            std::cout << "├─ " << app_name << " (circular dependency)\n";
            return;
        }
        
        visited.insert(app_name);
        
        // Print app name with status
        auto status = get_app_status(app_name);
        auto version = get_app_version(app_name);
        
        std::cout << "├─ " << app_name;
        if (!version.empty()) {
            std::cout << " (" << version << ")";
        }
        std::cout << " [" << status << "]\n";
        
        // Get dependencies
        auto manifest = ManifestParser::find_and_parse(app_name);
        if (manifest.is_valid() && !manifest.depends.empty()) {
            for (const auto& dep : manifest.depends) {
                show_dependency_tree_recursive(dep, depth + 1, visited);
            }
        }
        
        visited.erase(app_name);
    }
    
    std::string get_app_status(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (std::filesystem::exists(app_dir)) {
            return "installed";
        } else {
            return "not installed";
        }
    }
    
    std::string get_app_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            // Get version from manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (manifest.is_valid()) {
                return manifest.version;
            }
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
};

} // namespace sco
