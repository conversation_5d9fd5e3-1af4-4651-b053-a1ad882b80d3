#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include <algorithm>
#include "../utils/output.hpp"

namespace sco {

class CleanupCommand : public BaseCommand {
public:
    CleanupCommand() = default;
    
    int execute() override {
        try {
            output::debug("Cleanup command called");
            
            if (cleanup_all_) {
                return cleanup_all_apps();
            } else if (!app_names_.empty()) {
                return cleanup_specific_apps();
            } else {
                std::cout << "No apps specified for cleanup.\n";
                show_usage();
                return 1;
            }
            
        } catch (const std::exception& e) {
            output::error("Cleanup command failed: {}", e.what());
            std::cerr << "Cleanup failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "cleanup"; }
    std::string get_description() const override { return "Cleanup apps by removing old versions"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    void set_cleanup_all(bool cleanup_all) { cleanup_all_ = cleanup_all; }
    void set_global(bool global) { global_ = global; }
    void set_keep_versions(int keep) { keep_versions_ = keep; }
    
private:
    std::vector<std::string> app_names_;
    bool cleanup_all_ = false;
    bool global_ = false;
    int keep_versions_ = 1; // Keep the current version by default
    
    void show_usage() {
        std::cout << "Usage:\n";
        std::cout << "  sco cleanup <app1> [app2] ...  # Cleanup specific apps\n";
        std::cout << "  sco cleanup --all              # Cleanup all apps\n";
        std::cout << "  sco cleanup --global           # Cleanup globally installed apps\n";
        std::cout << "  sco cleanup --keep <n>         # Keep n versions (default: 1)\n";
    }
    
    int cleanup_all_apps() {
        std::cout << "Cleaning up all installed apps...\n\n";
        
        auto installed_apps = get_installed_apps();
        if (installed_apps.empty()) {
            std::cout << "No apps are currently installed.\n";
            return 0;
        }
        
        return cleanup_apps(installed_apps);
    }
    
    int cleanup_specific_apps() {
        std::cout << "Cleaning up specific apps: ";
        for (size_t i = 0; i < app_names_.size(); ++i) {
            std::cout << app_names_[i];
            if (i < app_names_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";
        
        // Check which apps are installed
        auto installed_apps = get_installed_apps();
        std::vector<std::string> apps_to_cleanup;
        std::vector<std::string> not_installed;
        
        for (const auto& app : app_names_) {
            if (std::find(installed_apps.begin(), installed_apps.end(), app) != installed_apps.end()) {
                apps_to_cleanup.push_back(app);
            } else {
                not_installed.push_back(app);
            }
        }
        
        // Report apps that are not installed
        if (!not_installed.empty()) {
            std::cout << "The following apps are not installed:\n";
            for (const auto& app : not_installed) {
                std::cout << "  - " << app << "\n";
            }
            std::cout << "\n";
        }
        
        if (apps_to_cleanup.empty()) {
            std::cout << "No specified apps are currently installed.\n";
            return 1;
        }
        
        return cleanup_apps(apps_to_cleanup);
    }
    
    struct CleanupInfo {
        std::string app_name;
        std::vector<std::string> versions_to_remove;
        size_t space_freed = 0;
    };

    int cleanup_apps(const std::vector<std::string>& apps) {
        std::vector<CleanupInfo> cleanup_list;
        size_t total_space_freed = 0;
        
        // Analyze what can be cleaned up
        for (const auto& app : apps) {
            CleanupInfo info;
            info.app_name = app;
            
            auto versions = get_app_versions(app);
            if (versions.size() > keep_versions_) {
                // Sort versions (simple string sort, could be improved)
                std::sort(versions.begin(), versions.end());
                
                // Keep the latest versions
                for (size_t i = 0; i < versions.size() - keep_versions_; ++i) {
                    info.versions_to_remove.push_back(versions[i]);
                }
                
                // Calculate space that would be freed
                info.space_freed = calculate_space_to_free(app, info.versions_to_remove);
                total_space_freed += info.space_freed;
            }
            
            if (!info.versions_to_remove.empty()) {
                cleanup_list.push_back(info);
            }
        }
        
        if (cleanup_list.empty()) {
            std::cout << "Nothing to clean up. All apps have only current versions.\n";
            return 0;
        }
        
        // Show what will be cleaned up
        show_cleanup_summary(cleanup_list, total_space_freed);
        
        // Perform cleanup
        bool all_success = true;
        for (const auto& info : cleanup_list) {
            if (cleanup_app_versions(info.app_name, info.versions_to_remove)) {
                std::cout << "+ Cleaned up " << info.app_name << " ("
                         << info.versions_to_remove.size() << " old version(s))\n";
            } else {
                std::cout << "- Failed to clean up " << info.app_name << "\n";
                all_success = false;
            }
        }
        
        if (all_success) {
            std::cout << "\nCleanup completed successfully.\n";
            std::cout << "Freed approximately " << format_size(total_space_freed) << " of disk space.\n";
        } else {
            std::cout << "\nCleanup completed with some errors.\n";
        }
        
        return all_success ? 0 : 1;
    }
    
    std::vector<std::string> get_installed_apps() {
        std::vector<std::string> apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    // Skip special directories
                    if (app_name != "scoop" && app_name != ".git") {
                        apps.push_back(app_name);
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error reading apps directory: {}", e.what());
        }
        
        return apps;
    }
    
    std::vector<std::string> get_app_versions(const std::string& app_name) {
        std::vector<std::string> versions;
        
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return versions;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "backup") {
                        versions.push_back(dir_name);
                    }
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            output::error("Error reading app versions for {}: {}", app_name, e.what());
        }
        
        return versions;
    }
    
    size_t calculate_space_to_free(const std::string& app_name, const std::vector<std::string>& versions) {
        size_t total_size = 0;
        
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        for (const auto& version : versions) {
            auto version_dir = app_dir / version;
            if (std::filesystem::exists(version_dir)) {
                try {
                    for (const auto& entry : std::filesystem::recursive_directory_iterator(version_dir)) {
                        if (entry.is_regular_file()) {
                            total_size += std::filesystem::file_size(entry);
                        }
                    }
                } catch (const std::exception& e) {
                    SPDLOG_DEBUG("Error calculating size for {}/{}: {}", app_name, version, e.what());
                }
            }
        }
        
        return total_size;
    }
    
    void show_cleanup_summary(const std::vector<CleanupInfo>& cleanup_list, size_t total_space) {
        std::cout << "The following old versions will be removed:\n\n";
        
        TableFormatter table;
        table.add_column("App", true);
        table.add_column("Versions to Remove", true);
        table.add_column("Space Freed", true);
        
        for (const auto& info : cleanup_list) {
            std::string versions_str;
            for (size_t i = 0; i < info.versions_to_remove.size(); ++i) {
                versions_str += info.versions_to_remove[i];
                if (i < info.versions_to_remove.size() - 1) versions_str += ", ";
            }
            
            table.add_row({
                info.app_name,
                versions_str,
                format_size(info.space_freed)
            });
        }
        
        table.print();
        
        std::cout << "\nTotal space to be freed: " << format_size(total_space) << "\n\n";
    }
    
    bool cleanup_app_versions(const std::string& app_name, const std::vector<std::string>& versions) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        bool all_success = true;
        
        for (const auto& version : versions) {
            auto version_dir = app_dir / version;
            if (std::filesystem::exists(version_dir)) {
                try {
                    std::filesystem::remove_all(version_dir);
                    output::debug("Removed version directory: {}", version_dir.string());
                } catch (const std::exception& e) {
                    output::error("Failed to remove version directory {}: {}", version_dir.string(), e.what());
                    all_success = false;
                }
            }
        }
        
        return all_success;
    }
    
    std::string format_size(size_t bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unit_index = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unit_index < 4) {
            size /= 1024.0;
            unit_index++;
        }
        
        char buffer[32];
        if (unit_index == 0) {
            snprintf(buffer, sizeof(buffer), "%.0f %s", size, units[unit_index]);
        } else {
            snprintf(buffer, sizeof(buffer), "%.1f %s", size, units[unit_index]);
        }
        
        return std::string(buffer);
    }
};

} // namespace sco
