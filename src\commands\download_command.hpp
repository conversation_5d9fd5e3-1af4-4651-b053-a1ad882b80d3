#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../utils/download_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include "../utils/output.hpp"

namespace sco {

class DownloadCommand : public BaseCommand {
public:
    DownloadCommand() = default;
    
    int execute() override {
        try {
            if (app_names_.empty()) {
                std::cerr << "App name(s) required.\n";
                std::cout << "Usage: sco download <app1> [app2] ...\n";
                return 1;
            }

            output::debug("Download command called for {} app(s)", app_names_.size());

            return download_apps();

        } catch (const std::exception& e) {
            output::error("Download command failed: {}", e.what());
            std::cerr << "Download failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "download"; }
    std::string get_description() const override { return "Download apps in the cache folder and verify hashes"; }
    
    void set_app_names(const std::vector<std::string>& app_names) { app_names_ = app_names; }
    void set_architecture(const std::string& arch) { architecture_ = arch; }
    void set_force(bool force) { force_ = force; }
    
private:
    std::vector<std::string> app_names_;
    std::string architecture_ = "64bit";
    bool force_ = false;
    
    struct DownloadInfo {
        std::string app_name;
        std::string version;
        std::vector<ManifestUrl> urls;
        bool success = false;
        std::string error_message;
    };

    int download_apps() {
        std::cout << "Downloading apps: ";
        for (size_t i = 0; i < app_names_.size(); ++i) {
            std::cout << app_names_[i];
            if (i < app_names_.size() - 1) std::cout << ", ";
        }
        std::cout << "\n\n";

        std::vector<DownloadInfo> download_list;
        
        // Prepare download list
        for (const auto& app_name : app_names_) {
            DownloadInfo info;
            info.app_name = app_name;
            
            // Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                info.error_message = "Manifest not found";
                download_list.push_back(info);
                continue;
            }

            // Store version information
            info.version = manifest.version;

            // Get URLs for the specified architecture
            info.urls = manifest.get_urls(architecture_);
            if (info.urls.empty()) {
                info.error_message = "No download URLs found for " + architecture_;
                download_list.push_back(info);
                continue;
            }
            
            download_list.push_back(info);
        }
        
        // Show download summary
        show_download_summary(download_list);
        
        // Perform downloads
        bool all_success = true;
        for (auto& info : download_list) {
            if (info.urls.empty()) {
                std::cout << "- " << info.app_name << ": " << info.error_message << "\n";
                all_success = false;
                continue;
            }
            
            std::cout << "Downloading " << info.app_name << "...\n";
            
            bool app_success = true;
            for (size_t i = 0; i < info.urls.size(); ++i) {
                const auto& url_info = info.urls[i];
                std::cout << "  File " << (i + 1) << "/" << info.urls.size() << ": ";
                
                if (download_file(url_info, info.app_name, info.version)) {
                    std::cout << "OK\n";
                } else {
                    std::cout << "Failed\n";
                    app_success = false;
                }
            }
            
            if (app_success) {
                std::cout << "+ " << info.app_name << " downloaded successfully\n";
                info.success = true;
            } else {
                std::cout << "- " << info.app_name << " download failed\n";
                all_success = false;
            }
        }
        
        // Show final summary
        show_final_summary(download_list);
        
        return all_success ? 0 : 1;
    }
    
    void show_download_summary(const std::vector<DownloadInfo>& download_list) {
        std::cout << "Download Summary:\n";
        std::cout << "================\n";
        
        TableFormatter table;
        table.add_column("App", true);
        table.add_column("Files", true);
        table.add_column("Architecture", true);
        table.add_column("Status", true);
        
        for (const auto& info : download_list) {
            std::string status = info.urls.empty() ? "Error" : "Ready";
            table.add_row({
                info.app_name,
                std::to_string(info.urls.size()),
                architecture_,
                status
            });
        }
        
        table.print();
        std::cout << "\n";
    }
    
    void show_final_summary(const std::vector<DownloadInfo>& download_list) {
        std::cout << "\nDownload Results:\n";
        std::cout << "================\n";
        
        int successful = 0;
        int failed = 0;
        
        for (const auto& info : download_list) {
            if (info.success) {
                successful++;
            } else {
                failed++;
            }
        }
        
        std::cout << "Successful: " << successful << "\n";
        std::cout << "Failed: " << failed << "\n";
        std::cout << "Total: " << download_list.size() << "\n";
        
        if (successful > 0) {
            std::cout << "\nDownloaded files are cached and ready for installation.\n";
            std::cout << "Use 'sco install <app>' to install from cache.\n";
        }
    }
    
    bool download_file(const ManifestUrl& url_info, const std::string& app_name, const std::string& version) {
        auto& config = Config::instance();
        auto cache_dir = config.get_cache_dir();

        // Create cache directory if it doesn't exist
        std::filesystem::create_directories(cache_dir);

        // Generate Scoop-compatible filename: app#version#hash.ext
        std::string filename = DownloadManager::generate_cache_filename(
            app_name, version, url_info.url, url_info.hash);

        auto cache_file = cache_dir / filename;
        
        // Check if file already exists and hash matches
        if (std::filesystem::exists(cache_file) && !force_) {
            if (DownloadManager::verify_hash(cache_file, url_info.hash)) {
                SPDLOG_DEBUG("File already cached with correct hash: {}", cache_file.string());
                return true;
            } else {
                SPDLOG_WARN("Cached file hash mismatch, re-downloading");
                std::filesystem::remove(cache_file);
            }
        }
        
        // Download file
        SPDLOG_DEBUG("Downloading: {} -> {}", url_info.url, cache_file.string());
        auto result = DownloadManager::download_with_progress(url_info.url, cache_file);
        
        if (!result.success) {
            SPDLOG_ERROR("Download failed: {}", result.error_message);
            return false;
        }
        
        // Verify hash
        if (!url_info.hash.empty()) {
            if (!DownloadManager::verify_hash(cache_file, url_info.hash)) {
                SPDLOG_ERROR("Downloaded file hash verification failed");
                std::filesystem::remove(cache_file);
                return false;
            }
        }
        
        return true;
    }
};

} // namespace sco
