#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/shim_manager.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include <fstream>
#include <spdlog/spdlog.h>

namespace sco {

class ShimCommand : public BaseCommand {
public:
    ShimCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "list") {
                return list_shims();
            } else if (action_ == "add") {
                return add_shim();
            } else if (action_ == "remove" || action_ == "rm") {
                return remove_shim();
            } else if (action_ == "repair") {
                return repair_shims();
            } else {
                std::cout << "Unknown shim action: " << action_ << "\n";
                show_usage();
                return 1;
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Shim command failed: {}", e.what());
            std::cerr << "Shim command failed: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "shim"; }
    std::string get_description() const override { return "Manipulate Scoop shims"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_shim_name(const std::string& name) { shim_name_ = name; }
    void set_target_path(const std::string& path) { target_path_ = path; }
    
private:
    std::string action_;
    std::string shim_name_;
    std::string target_path_;
    
    void show_usage() {
        std::cout << "Usage:\n";
        std::cout << "  sco shim [list]                     # List all shims\n";
        std::cout << "  sco shim add <name> <target>        # Add a shim\n";
        std::cout << "  sco shim remove <name>              # Remove a shim\n";
        std::cout << "  sco shim repair                     # Repair all shims\n";
    }
    
    int list_shims() {
        auto shims = ShimManager::list_shims();
        
        if (shims.empty()) {
            std::cout << "No shims are currently installed.\n";
            return 0;
        }
        
        std::cout << "Installed shims:\n\n";
        
        TableFormatter table;
        table.add_column("Name", true);
        table.add_column("Target", true);
        table.add_column("Status", true);
        
        for (const auto& shim : shims) {
            auto target = shim.target_path.string();
            auto status = get_shim_status(shim.shim_path.string(), target);

            table.add_row({
                shim.name,
                target.empty() ? "Unknown" : target,
                status
            });
        }
        
        table.print();
        
        std::cout << "\nTotal: " << shims.size() << " shim(s)\n";
        return 0;
    }
    
    int add_shim() {
        if (shim_name_.empty() || target_path_.empty()) {
            std::cout << "Both shim name and target path are required.\n";
            std::cout << "Usage: sco shim add <name> <target>\n";
            return 1;
        }
        
        // Check if target exists
        if (!std::filesystem::exists(target_path_)) {
            std::cout << "Target file does not exist: " << target_path_ << "\n";
            return 1;
        }
        
        std::cout << "Creating shim '" << shim_name_ << "' -> '" << target_path_ << "'...\n";
        
        if (ShimManager::create_shim(shim_name_, target_path_)) {
            std::cout << "Shim created successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to create shim.\n";
            return 1;
        }
    }
    
    int remove_shim() {
        if (shim_name_.empty()) {
            std::cout << "Shim name is required.\n";
            std::cout << "Usage: sco shim remove <name>\n";
            return 1;
        }
        
        std::cout << "Removing shim '" << shim_name_ << "'...\n";
        
        if (ShimManager::remove_shim(shim_name_)) {
            std::cout << "Shim removed successfully.\n";
            return 0;
        } else {
            std::cout << "Failed to remove shim (may not exist).\n";
            return 1;
        }
    }
    
    int repair_shims() {
        std::cout << "Repairing all shims...\n\n";
        
        auto shims = ShimManager::list_shims();
        
        if (shims.empty()) {
            std::cout << "No shims found to repair.\n";
            return 0;
        }
        
        int repaired = 0;
        int failed = 0;
        
        for (const auto& shim : shims) {
            auto shim_name = shim.name;
            auto target = shim.target_path.string();

            std::cout << "Checking " << shim_name << "... ";

            if (target.empty()) {
                std::cout << "Cannot determine target, skipping\n";
                failed++;
                continue;
            }

            if (!std::filesystem::exists(target)) {
                std::cout << "Target missing, removing shim\n";
                ShimManager::remove_shim(shim_name);
                repaired++;
                continue;
            }

            // Check if shim is working
            if (is_shim_working(shim.shim_path.string())) {
                std::cout << "OK\n";
            } else {
                std::cout << "Broken, recreating... ";

                // Remove and recreate
                ShimManager::remove_shim(shim_name);
                if (ShimManager::create_shim(shim_name, target)) {
                    std::cout << "Fixed\n";
                    repaired++;
                } else {
                    std::cout << "Failed\n";
                    failed++;
                }
            }
        }
        
        std::cout << "\nRepair completed:\n";
        std::cout << "  Repaired: " << repaired << "\n";
        std::cout << "  Failed: " << failed << "\n";
        std::cout << "  Total: " << shims.size() << "\n";
        
        return failed == 0 ? 0 : 1;
    }
    
    std::string get_shim_target(const std::string& shim_path) {
        try {
            std::ifstream file(shim_path);
            if (!file.is_open()) {
                return "";
            }
            
            std::string line;
            while (std::getline(file, line)) {
                // Look for common shim patterns
                if (line.find("@\"") != std::string::npos && line.find("\"") != std::string::npos) {
                    // Extract path between quotes
                    size_t start = line.find("@\"") + 2;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }
                
                // Look for PowerShell shim pattern
                if (line.find("& \"") != std::string::npos) {
                    size_t start = line.find("& \"") + 3;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_DEBUG("Failed to read shim target: {}", e.what());
        }
        
        return "";
    }
    
    std::string get_shim_status(const std::string& shim_path, const std::string& target) {
        if (target.empty()) {
            return "Unknown target";
        }
        
        if (!std::filesystem::exists(target)) {
            return "Target missing";
        }
        
        if (!is_shim_working(shim_path)) {
            return "Broken";
        }
        
        return "OK";
    }
    
    bool is_shim_working(const std::string& shim_path) {
        // Basic check: shim file exists and is readable
        if (!std::filesystem::exists(shim_path)) {
            return false;
        }
        
        try {
            std::ifstream file(shim_path);
            return file.is_open();
        } catch (const std::exception&) {
            return false;
        }
    }
};

} // namespace sco
