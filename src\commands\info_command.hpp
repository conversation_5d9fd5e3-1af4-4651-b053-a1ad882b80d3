#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../core/manifest.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class InfoCommand : public BaseCommand {
public:
    InfoCommand() = default;
    
    int execute() override {
        try {
            if (app_name_.empty()) {
                std::cerr << "App name is required.\n";
                std::cout << "Usage: sco info <app_name>\n";
                return 1;
            }

            output::debug("Getting info for app: {}", app_name_);

            // Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name_);
            if (!manifest.is_valid()) {
                std::cout << "App '" << app_name_ << "' not found.\n";
                std::cout << "Try 'sco search " << app_name_ << "' to find similar apps.\n";
                return 1;
            }

            show_app_info(manifest);
            return 0;

        } catch (const std::exception& e) {
            output::error("Info command failed: {}", e.what());
            std::cerr << "Failed to get app info: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "info"; }
    std::string get_description() const override { return "Display information about an app"; }
    
    void set_app_name(const std::string& app_name) { app_name_ = app_name; }
    
private:
    std::string app_name_;
    
    void show_app_info(const Manifest& manifest) {
        std::cout << "App: " << manifest.name << "\n";
        std::cout << std::string(50, '=') << "\n\n";
        
        // Basic information
        show_basic_info(manifest);
        
        // Installation status
        show_installation_status(manifest.name);
        
        // Dependencies
        show_dependencies(manifest);
        
        // Download information
        show_download_info(manifest);
        
        // Binary information
        show_binary_info(manifest);
        
        // Scripts information
        show_scripts_info(manifest);
        
        // Manifest location
        show_manifest_location(manifest);
    }
    
    void show_basic_info(const Manifest& manifest) {
        std::cout << "Basic Information:\n";
        std::cout << "  Name:        " << manifest.name << "\n";
        std::cout << "  Version:     " << manifest.version << "\n";
        std::cout << "  Description: " << manifest.description << "\n";
        
        if (!manifest.homepage.empty()) {
            std::cout << "  Homepage:    " << manifest.homepage << "\n";
        }
        
        if (!manifest.license.empty()) {
            std::cout << "  License:     " << manifest.license << "\n";
        }
        
        std::cout << "  Source:      " << manifest.bucket << " bucket\n";
        std::cout << "\n";
    }
    
    void show_installation_status(const std::string& app_name) {
        std::cout << "Installation Status:\n";
        
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            std::cout << "  Status:      Not installed\n";
        } else {
            std::cout << "  Status:      Installed\n";
            
            // Get installed version
            auto installed_version = get_installed_version(app_name);
            if (!installed_version.empty()) {
                std::cout << "  Installed:   " << installed_version << "\n";
            }
            
            // Get installation path
            std::cout << "  Location:    " << app_dir.string() << "\n";
            
            // Get installation date
            auto install_date = get_installation_date(app_name);
            if (!install_date.empty()) {
                std::cout << "  Installed:   " << install_date << "\n";
            }
            
            // Check for available versions
            show_available_versions(app_name);
        }
        
        std::cout << "\n";
    }
    
    void show_dependencies(const Manifest& manifest) {
        std::cout << "Dependencies:\n";
        
        if (manifest.depends.empty()) {
            std::cout << "  None\n";
        } else {
            for (const auto& dep : manifest.depends) {
                std::cout << "  - " << dep;
                
                // Check if dependency is installed
                auto& config = Config::instance();
                auto dep_dir = config.get_apps_dir() / dep;
                if (std::filesystem::exists(dep_dir)) {
                    std::cout << " (installed)";
                } else {
                    std::cout << " (not installed)";
                }
                std::cout << "\n";
            }
        }
        
        std::cout << "\n";
    }
    
    void show_download_info(const Manifest& manifest) {
        std::cout << "Download Information:\n";
        
        // Show URLs for different architectures
        std::vector<std::string> architectures = {"64bit", "32bit", "arm64"};
        
        for (const auto& arch : architectures) {
            auto urls = manifest.get_urls(arch);
            if (!urls.empty()) {
                std::cout << "  " << arch << ":\n";
                for (size_t i = 0; i < urls.size(); ++i) {
                    std::cout << "    URL " << (i + 1) << ": " << urls[i].url << "\n";
                    if (!urls[i].hash.empty()) {
                        std::cout << "    Hash:  " << urls[i].hash << "\n";
                    }
                }
            }
        }
        
        std::cout << "\n";
    }
    
    void show_binary_info(const Manifest& manifest) {
        std::cout << "Executables:\n";
        
        auto bin_entries = manifest.get_bin("64bit"); // Default to 64bit
        if (bin_entries.empty()) {
            std::cout << "  None\n";
        } else {
            for (const auto& bin : bin_entries) {
                std::cout << "  - " << bin << "\n";
            }
        }
        
        std::cout << "\n";
    }
    
    void show_scripts_info(const Manifest& manifest) {
        std::cout << "Scripts:\n";
        
        auto pre_install = manifest.get_pre_install("64bit");
        auto post_install = manifest.get_post_install("64bit");
        auto pre_uninstall = manifest.get_pre_uninstall("64bit");
        auto post_uninstall = manifest.get_post_uninstall("64bit");
        
        if (pre_install.empty() && post_install.empty() && 
            pre_uninstall.empty() && post_uninstall.empty()) {
            std::cout << "  None\n";
        } else {
            if (!pre_install.empty()) {
                std::cout << "  Pre-install:   Yes\n";
            }
            if (!post_install.empty()) {
                std::cout << "  Post-install:  Yes\n";
            }
            if (!pre_uninstall.empty()) {
                std::cout << "  Pre-uninstall: Yes\n";
            }
            if (!post_uninstall.empty()) {
                std::cout << "  Post-uninstall: Yes\n";
            }
        }
        
        std::cout << "\n";
    }
    
    void show_manifest_location(const Manifest& manifest) {
        std::cout << "Manifest:\n";
        
        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();
        auto manifest_path = buckets_dir / manifest.bucket / "bucket" / (manifest.name + ".json");
        
        if (std::filesystem::exists(manifest_path)) {
            std::cout << "  Location: " << manifest_path.string() << "\n";
            
            // Show file size and modification time
            try {
                auto file_size = std::filesystem::file_size(manifest_path);
                auto mod_time = std::filesystem::last_write_time(manifest_path);
                
                std::cout << "  Size:     " << file_size << " bytes\n";
                
                // Convert file time to system time (simplified)
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    mod_time - std::filesystem::file_time_type::clock::now() + 
                    std::chrono::system_clock::now());
                auto time_t = std::chrono::system_clock::to_time_t(sctp);
                
                std::cout << "  Modified: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
                
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to get manifest file info: {}", e.what());
            }
        }
        
        std::cout << "\n";
    }
    
    std::string get_installed_version(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("version")) {
                    return json["version"].get<std::string>();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation info for {}: {}", app_name, e.what());
            }
        }
        
        return "unknown";
    }
    
    std::string get_installation_date(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return "";
        }
        
        // Try to read from installation info
        auto info_file = app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = app_dir / "scoop-install.json";
        }
        
        if (std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;
                
                if (json.contains("install_time")) {
                    auto timestamp = json["install_time"].get<int64_t>();
                    auto time_t = static_cast<std::time_t>(timestamp);
                    
                    std::ostringstream oss;
                    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
                    return oss.str();
                }
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Failed to read installation date for {}: {}", app_name, e.what());
            }
        }
        
        return "";
    }
    
    void show_available_versions(const std::string& app_name) {
        auto& config = Config::instance();
        auto app_dir = config.get_apps_dir() / app_name;
        
        if (!std::filesystem::exists(app_dir)) {
            return;
        }
        
        try {
            std::vector<std::string> versions;
            
            // Collect all version directories
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory()) {
                    std::string dir_name = entry.path().filename().string();
                    // Skip special directories
                    if (dir_name != "current" && dir_name != "backup") {
                        versions.push_back(dir_name);
                    }
                }
            }
            
            if (versions.size() > 1) {
                std::cout << "  Versions:    ";
                for (size_t i = 0; i < versions.size(); ++i) {
                    std::cout << versions[i];
                    if (i < versions.size() - 1) std::cout << ", ";
                }
                std::cout << "\n";
            }
            
        } catch (const std::exception& e) {
            SPDLOG_DEBUG("Failed to get available versions for {}: {}", app_name, e.what());
        }
    }
};

} // namespace sco
