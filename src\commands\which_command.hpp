#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <string>
#include <vector>
#include <cstdlib>
#include "../utils/output.hpp"

#ifdef _WIN32
#include <windows.h>
#endif

namespace sco {

class WhichCommand : public BaseCommand {
public:
    WhichCommand() = default;
    
    int execute() override {
        try {
            if (command_name_.empty()) {
                std::cerr << "Command name is required.\n";
                std::cout << "Usage: sco which <command>\n";
                return 1;
            }

            SPDLOG_DEBUG("Locating command: {}", command_name_);

            return locate_command();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("Which command failed: {}", e.what());
            std::cerr << "Failed to locate command: " << e.what() << std::endl;
            return 1;
        }
    }
    
    std::string get_name() const override { return "which"; }
    std::string get_description() const override { return "Locate a shim/executable (similar to 'which' on Linux)"; }
    
    void set_command_name(const std::string& command_name) { command_name_ = command_name; }
    
private:
    std::string command_name_;
    
    int locate_command() {
        // First check if it's a Scoop shim
        auto shim_path = find_scoop_shim();
        if (!shim_path.empty()) {
            std::cout << shim_path.string() << std::endl;
            
            // Also show the target executable
            auto target = get_shim_target(shim_path);
            if (!target.empty()) {
                std::cout << "Target: " << target << std::endl;
            }
            
            return 0;
        }
        
        // Check if it's in the system PATH
        auto system_path = find_in_system_path();
        if (!system_path.empty()) {
            std::cout << system_path.string() << std::endl;
            return 0;
        }
        
        std::cout << "Command '" << command_name_ << "' not found.\n";
        return 1;
    }
    
    std::filesystem::path find_scoop_shim() {
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        if (!std::filesystem::exists(shims_dir)) {
            return {};
        }
        
        // Check for .exe extension
        auto shim_path = shims_dir / (command_name_ + ".exe");
        if (std::filesystem::exists(shim_path)) {
            return shim_path;
        }
        
        // Check without extension
        shim_path = shims_dir / command_name_;
        if (std::filesystem::exists(shim_path)) {
            return shim_path;
        }
        
        // Check for .cmd extension
        shim_path = shims_dir / (command_name_ + ".cmd");
        if (std::filesystem::exists(shim_path)) {
            return shim_path;
        }
        
        // Check for .bat extension
        shim_path = shims_dir / (command_name_ + ".bat");
        if (std::filesystem::exists(shim_path)) {
            return shim_path;
        }
        
        return {};
    }
    
    std::filesystem::path find_in_system_path() {
        // Get PATH environment variable
        std::string path_env;
        
#ifdef _WIN32
        char* path_ptr = nullptr;
        size_t path_size = 0;
        if (_dupenv_s(&path_ptr, &path_size, "PATH") == 0 && path_ptr != nullptr) {
            path_env = path_ptr;
            free(path_ptr);
        }
#else
        const char* path_ptr = std::getenv("PATH");
        if (path_ptr != nullptr) {
            path_env = path_ptr;
        }
#endif
        
        if (path_env.empty()) {
            return {};
        }
        
        // Split PATH by delimiter
        std::vector<std::string> path_dirs;
        std::string delimiter = 
#ifdef _WIN32
            ";";
#else
            ":";
#endif
        
        size_t start = 0;
        size_t end = path_env.find(delimiter);
        
        while (end != std::string::npos) {
            path_dirs.push_back(path_env.substr(start, end - start));
            start = end + delimiter.length();
            end = path_env.find(delimiter, start);
        }
        path_dirs.push_back(path_env.substr(start));
        
        // Search in each directory
        std::vector<std::string> extensions;
#ifdef _WIN32
        extensions = {".exe", ".cmd", ".bat", ".com", ""};
#else
        extensions = {""};
#endif
        
        for (const auto& dir : path_dirs) {
            if (dir.empty()) continue;
            
            std::filesystem::path dir_path(dir);
            if (!std::filesystem::exists(dir_path)) continue;
            
            for (const auto& ext : extensions) {
                auto exe_path = dir_path / (command_name_ + ext);
                if (std::filesystem::exists(exe_path) && 
                    std::filesystem::is_regular_file(exe_path)) {
                    return exe_path;
                }
            }
        }
        
        return {};
    }
    
    std::string get_shim_target(const std::filesystem::path& shim_path) {
        try {
            // Try to read shim content to find target
            std::ifstream file(shim_path);
            if (!file.is_open()) {
                return "";
            }
            
            std::string line;
            while (std::getline(file, line)) {
                // Look for common shim patterns
                if (line.find("@\"") != std::string::npos && line.find("\"") != std::string::npos) {
                    // Extract path between quotes
                    size_t start = line.find("@\"") + 2;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }
                
                // Look for PowerShell shim pattern
                if (line.find("& \"") != std::string::npos) {
                    size_t start = line.find("& \"") + 3;
                    size_t end = line.find("\"", start);
                    if (end != std::string::npos) {
                        return line.substr(start, end - start);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            SPDLOG_DEBUG("Failed to read shim target: {}", e.what());
        }
        
        return "";
    }
};

} // namespace sco
